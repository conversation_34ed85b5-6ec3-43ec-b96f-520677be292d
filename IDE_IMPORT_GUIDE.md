# StormWeaver IDE导入指南

## 🚀 快速导入步骤

### IntelliJ IDEA 导入

1. **打开IntelliJ IDEA**
2. **选择 "Open or Import"**
3. **导航到项目文件夹** `D:/mcmodding/StormWeaver`
4. **选择 `build.gradle` 文件**
5. **点击 "Open as Project"**
6. **在导入对话框中**：
   - ✅ 选择 "Use Gradle from: 'gradle-wrapper.properties' file"
   - ✅ 选择 "Gradle JVM: Project SDK (Java 17)"
   - ✅ 勾选 "Create directories for empty content roots automatically"
7. **点击 "OK"**

### Eclipse 导入

1. **打开Eclipse**
2. **File → Import**
3. **选择 "Gradle → Existing Gradle Project"**
4. **点击 "Next"**
5. **Project root directory**: 选择 `D:/mcmodding/StormWeaver`
6. **点击 "Finish"**

### Visual Studio Code 导入

1. **打开VS Code**
2. **File → Open Folder**
3. **选择 `D:/mcmodding/StormWeaver` 文件夹**
4. **安装推荐的扩展**：
   - Extension Pack for Java
   - Gradle for Java

## 🔧 如果遇到Gradle问题

### 方法1: 使用本地Gradle

如果Gradle Wrapper有问题，可以使用本地安装的Gradle：

1. **下载并安装Gradle 8.1.1**
   - 访问: https://gradle.org/releases/
   - 下载 gradle-8.1.1-bin.zip
   - 解压到本地目录（如 `C:/gradle`）
   - 添加 `C:/gradle/bin` 到系统PATH

2. **在IDE中配置**：
   - IntelliJ: Settings → Build → Gradle → Use local gradle distribution
   - Eclipse: Window → Preferences → Gradle → Gradle distribution

### 方法2: 重新生成Gradle Wrapper

在项目根目录运行：
```bash
gradle wrapper --gradle-version 8.1.1
```

### 方法3: 手动下载依赖

如果网络问题导致依赖下载失败：

1. **配置镜像源** - 在 `build.gradle` 的 repositories 块中添加：
```gradle
repositories {
    maven { url 'https://maven.aliyun.com/repository/public/' }
    maven { url 'https://maven.aliyun.com/repository/central/' }
    // 其他仓库...
}
```

## 📋 项目结构验证

导入成功后，您应该看到以下结构：

```
StormWeaver/
├── src/
│   └── main/
│       ├── java/
│       │   └── com/stormweaver/stormweaver/
│       │       ├── StormWeaver.java
│       │       ├── Config.java
│       │       ├── data/
│       │       ├── molang/
│       │       ├── particle/
│       │       ├── network/
│       │       └── commands/
│       └── resources/
│           ├── META-INF/
│           └── assets/
├── build.gradle
├── settings.gradle
└── gradle.properties
```

## 🎯 运行配置

### IntelliJ IDEA

1. **Gradle面板** → Tasks → forgegradle runs → runClient
2. **或者创建运行配置**：
   - Run → Edit Configurations
   - 添加 Gradle 配置
   - Tasks: `runClient`
   - Working directory: 项目根目录

### Eclipse

1. **Gradle Tasks视图** → 展开项目 → forgegradle runs → runClient
2. **双击运行**

## 🐛 常见问题解决

### 问题1: "Could not find ForgeGradle"
**解决方案**: 检查网络连接，确保能访问Maven仓库

### 问题2: "Java版本不兼容"
**解决方案**: 确保使用Java 17，在IDE中设置Project SDK

### 问题3: "Gradle同步失败"
**解决方案**: 
1. 删除 `.gradle` 文件夹
2. 重新导入项目
3. 或使用 `gradle clean build`

### 问题4: "找不到Minecraft类"
**解决方案**: 运行 `gradle genEclipseRuns` 或 `gradle genIntellijRuns`

## 📞 获取帮助

如果仍然遇到问题：

1. **检查日志**: 查看IDE的构建日志和错误信息
2. **清理重建**: 删除 `build` 文件夹，重新构建
3. **检查网络**: 确保能访问Maven Central和Forge仓库
4. **版本兼容**: 确认Java 17 + Gradle 8.1.1 + Forge 47.2.0

## ✅ 验证安装

导入成功后，尝试：

1. **编译项目**: `gradle build`
2. **运行客户端**: `gradle runClient`
3. **在游戏中测试**: `/stormweaver particle test_smoke`

如果这些步骤都成功，说明项目已正确导入！
