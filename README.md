# StormWeaver - 基岩版粒子系统转换器

StormWeaver是一个强大的Minecraft Forge模组，专门用于将Minecraft基岩版的粒子效果JSON配置文件转换为Java版Forge的粒子API调用。

## 🌟 核心功能

- **基岩版粒子JSON解析**: 完整支持基岩版粒子系统的JSON格式
- **MoLang表达式引擎**: 基于exp4j的高性能表达式求值系统
- **数据驱动粒子系统**: 无需重新编译即可添加新的粒子效果
- **动态纹理支持**: 支持运行时纹理加载和切换
- **网络同步**: 完整的多人游戏支持
- **性能优化**: 智能缓存和懒加载机制

## 🚀 技术特性

### MoLang表达式支持
- 完整的数学函数库（sin, cos, lerp, clamp等）
- 变量系统（particle_age, emitter_random等）
- 高性能表达式编译和缓存
- 角度/弧度自动转换

### 组件化架构
- **发射器组件**: 控制粒子生成（速率、形状、生命周期）
- **粒子组件**: 控制单个粒子行为（运动、外观、生命周期）
- **模块化设计**: 支持组件的任意组合

### 性能优化
- 表达式预编译和缓存
- 智能资源管理
- 可配置的性能限制
- 距离剔除和LOD支持

## 📦 安装方法

1. 确保已安装Minecraft 1.20.1和Forge 47.2.0+
2. 下载StormWeaver模组文件
3. 将模组文件放入`mods`文件夹
4. 启动游戏

## 🎮 使用方法

### 基本命令

```
/stormweaver particle <effect_id>  # 在当前位置生成粒子效果
/stormweaver reload                # 重载粒子效果配置
/stormweaver info                  # 显示系统信息
```

### 创建粒子效果

1. 在`assets/stormweaver/blizzard_particles/`目录下创建JSON文件
2. 使用基岩版粒子JSON格式编写效果
3. 使用命令测试效果

### 示例粒子效果

```json
{
  "format_version": "1.10.0",
  "particle_effect": {
    "description": {
      "identifier": "stormweaver:test_smoke",
      "basic_render_parameters": {
        "material": "particles_alpha",
        "texture": "textures/particle/generic_0"
      }
    },
    "components": {
      "minecraft:emitter_rate_steady": {
        "spawn_rate": 5,
        "max_particles": 50
      },
      "minecraft:emitter_lifetime_once": {
        "active_time": 3.0
      },
      "minecraft:emitter_shape_sphere": {
        "offset": [0, 0, 0],
        "radius": 0.5,
        "surface_only": false,
        "direction": "outwards"
      },
      "minecraft:particle_lifetime_expression": {
        "max_lifetime": "Math.random(1.0, 3.0)"
      },
      "minecraft:particle_motion_dynamic": {
        "linear_acceleration": [0, 0.5, 0],
        "linear_drag_coefficient": 0.1
      },
      "minecraft:particle_appearance_billboard": {
        "size": ["0.1 + variable.particle_age * 0.05", "0.1 + variable.particle_age * 0.05"],
        "facing_camera_mode": "lookat_xyz"
      }
    }
  }
}
```

## ⚙️ 配置选项

模组提供了丰富的配置选项，可在配置文件中调整：

### 性能设置
- `maxParticlesPerEmitter`: 每个发射器的最大粒子数
- `maxActiveEmitters`: 同时活跃的发射器数量
- `particleRenderDistance`: 粒子渲染距离

### 调试选项
- `enableDebugLogging`: 启用详细日志
- `showParticleDebugInfo`: 显示调试信息
- `enablePerformanceMonitoring`: 性能监控

### 资源管理
- `enableResourceCaching`: 启用资源缓存
- `cacheSizeLimit`: 缓存大小限制
- `preloadAllEffects`: 预加载所有效果

## 🔧 开发指南

### 项目结构

```
src/main/java/com/stormweaver/stormweaver/
├── StormWeaver.java              # 主模组类
├── Config.java                   # 配置管理
├── data/                         # 数据模型
│   ├── BlizzardParticleEffect.java
│   └── components/               # 组件定义
├── molang/                       # MoLang引擎
│   └── MoLangEvaluator.java
├── particle/                     # 粒子系统
│   ├── BlizzardParticleManager.java
│   ├── BlizzardParticle.java
│   └── BlizzardParticleEmitter.java
├── network/                      # 网络通信
└── commands/                     # 命令系统
```

### 技术架构

1. **JSON解析层**: 使用Gson解析基岩版粒子JSON
2. **MoLang引擎**: 基于exp4j的表达式求值系统
3. **粒子系统**: 发射器和粒子的分离式架构
4. **网络层**: 服务器到客户端的粒子同步
5. **渲染层**: 自定义粒子渲染器

### 扩展开发

要添加新的组件支持：

1. 在`data/components/`下创建组件类
2. 在`ParticleComponents.java`中添加字段
3. 在粒子类中添加处理逻辑
4. 更新MoLang变量和函数

## 🐛 故障排除

### 常见问题

**Q: 粒子效果不显示**
A: 检查JSON格式是否正确，确保材质路径存在

**Q: 性能问题**
A: 调整配置中的粒子数量限制，启用距离剔除

**Q: MoLang表达式错误**
A: 检查表达式语法，确保变量名正确

### 调试技巧

1. 启用调试日志查看详细信息
2. 使用`/stormweaver info`检查系统状态
3. 检查控制台输出的错误信息

## 📚 参考资料

- [Minecraft基岩版粒子文档](https://bedrock.dev/docs/stable/Particles)
- [MoLang语言参考](https://bedrock.dev/docs/stable/Molang)
- [Forge开发文档](https://docs.minecraftforge.net/)

## 🤝 贡献

欢迎提交Issue和Pull Request！

## 📄 许可证

本项目采用MIT许可证。

## 👨‍💻 作者

**Ultimate_Kevin** - 项目创建者和主要开发者

---

*让Java版Minecraft也能享受基岩版丰富的粒子效果！*
