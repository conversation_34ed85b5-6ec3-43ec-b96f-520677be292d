package com.stormweaver.stormweaver.network;

import com.stormweaver.stormweaver.Config;
import com.stormweaver.stormweaver.StormWeaver;
import com.stormweaver.stormweaver.particle.BlizzardParticleManager;
import net.minecraft.network.FriendlyByteBuf;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.world.level.Level;
import net.minecraft.world.phys.Vec3;
import net.minecraftforge.api.distmarker.Dist;
import net.minecraftforge.fml.DistExecutor;
import net.minecraftforge.network.NetworkEvent;

import java.util.function.Supplier;

/**
 * 生成粒子效果网络数据包
 * 
 * 这个数据包用于从服务器向客户端发送粒子效果生成请求
 * 包含以下信息：
 * - 粒子效果ID（ResourceLocation）
 * - 生成位置（Vec3）
 * - 可选的额外参数
 * 
 * 工作流程：
 * 1. 服务器端发生需要粒子效果的事件
 * 2. 服务器构建SpawnParticleEffectPacket
 * 3. 发送给相关客户端
 * 4. 客户端接收并处理，生成粒子效果
 * 
 * <AUTHOR>
 */
public class SpawnParticleEffectPacket {
    
    // 粒子效果ID
    private final ResourceLocation effectId;
    
    // 生成位置
    private final Vec3 position;
    
    // 可选的初始速度
    private final Vec3 velocity;
    
    // 可选的额外数据
    private final String extraData;
    
    /**
     * 构造函数
     * 
     * @param effectId 粒子效果ID
     * @param position 生成位置
     */
    public SpawnParticleEffectPacket(ResourceLocation effectId, Vec3 position) {
        this(effectId, position, Vec3.ZERO, null);
    }
    
    /**
     * 构造函数
     * 
     * @param effectId 粒子效果ID
     * @param position 生成位置
     * @param velocity 初始速度
     */
    public SpawnParticleEffectPacket(ResourceLocation effectId, Vec3 position, Vec3 velocity) {
        this(effectId, position, velocity, null);
    }
    
    /**
     * 完整构造函数
     * 
     * @param effectId 粒子效果ID
     * @param position 生成位置
     * @param velocity 初始速度
     * @param extraData 额外数据
     */
    public SpawnParticleEffectPacket(ResourceLocation effectId, Vec3 position, Vec3 velocity, String extraData) {
        this.effectId = effectId;
        this.position = position;
        this.velocity = velocity != null ? velocity : Vec3.ZERO;
        this.extraData = extraData;
    }
    
    /**
     * 编码数据包到缓冲区
     * 
     * @param packet 数据包实例
     * @param buffer 网络缓冲区
     */
    public static void encode(SpawnParticleEffectPacket packet, FriendlyByteBuf buffer) {
        try {
            // 写入效果ID
            buffer.writeResourceLocation(packet.effectId);
            
            // 写入位置
            buffer.writeDouble(packet.position.x);
            buffer.writeDouble(packet.position.y);
            buffer.writeDouble(packet.position.z);
            
            // 写入速度
            buffer.writeDouble(packet.velocity.x);
            buffer.writeDouble(packet.velocity.y);
            buffer.writeDouble(packet.velocity.z);
            
            // 写入额外数据
            buffer.writeBoolean(packet.extraData != null);
            if (packet.extraData != null) {
                buffer.writeUtf(packet.extraData, 32767);
            }
            
        } catch (Exception e) {
            StormWeaver.LOGGER.error("编码粒子效果数据包失败", e);
        }
    }
    
    /**
     * 从缓冲区解码数据包
     * 
     * @param buffer 网络缓冲区
     * @return 解码后的数据包
     */
    public static SpawnParticleEffectPacket decode(FriendlyByteBuf buffer) {
        try {
            // 读取效果ID
            ResourceLocation effectId = buffer.readResourceLocation();
            
            // 读取位置
            double x = buffer.readDouble();
            double y = buffer.readDouble();
            double z = buffer.readDouble();
            Vec3 position = new Vec3(x, y, z);
            
            // 读取速度
            double vx = buffer.readDouble();
            double vy = buffer.readDouble();
            double vz = buffer.readDouble();
            Vec3 velocity = new Vec3(vx, vy, vz);
            
            // 读取额外数据
            String extraData = null;
            if (buffer.readBoolean()) {
                extraData = buffer.readUtf(32767);
            }
            
            return new SpawnParticleEffectPacket(effectId, position, velocity, extraData);
            
        } catch (Exception e) {
            StormWeaver.LOGGER.error("解码粒子效果数据包失败", e);
            return null;
        }
    }
    
    /**
     * 处理数据包
     * 
     * @param packet 数据包实例
     * @param contextSupplier 网络上下文提供者
     */
    public static void handle(SpawnParticleEffectPacket packet, Supplier<NetworkEvent.Context> contextSupplier) {
        NetworkEvent.Context context = contextSupplier.get();
        
        // 确保在主线程上处理
        context.enqueueWork(() -> {
            // 只在客户端处理
            DistExecutor.unsafeRunWhenOn(Dist.CLIENT, () -> () -> handleClientSide(packet, context));
        });
        
        context.setPacketHandled(true);
    }
    
    /**
     * 客户端处理逻辑
     * 
     * @param packet 数据包
     * @param context 网络上下文
     */
    private static void handleClientSide(SpawnParticleEffectPacket packet, NetworkEvent.Context context) {
        try {
            if (packet == null) {
                StormWeaver.LOGGER.warn("接收到空的粒子效果数据包");
                return;
            }
            
            // 获取客户端世界
            Level level = net.minecraft.client.Minecraft.getInstance().level;
            if (level == null) {
                StormWeaver.LOGGER.warn("客户端世界为null，无法生成粒子效果");
                return;
            }
            
            // 验证数据包内容
            if (packet.effectId == null) {
                StormWeaver.LOGGER.warn("粒子效果ID为null");
                return;
            }
            
            if (packet.position == null) {
                StormWeaver.LOGGER.warn("粒子效果位置为null");
                return;
            }
            
            // 检查距离限制
            Vec3 playerPos = net.minecraft.client.Minecraft.getInstance().player.position();
            double distance = packet.position.distanceTo(playerPos);
            
            if (distance > Config.particleRenderDistance) {
                if (Config.enableDebugLogging) {
                    StormWeaver.LOGGER.debug("粒子效果距离过远，跳过渲染: {} > {}", 
                            distance, Config.particleRenderDistance);
                }
                return;
            }
            
            // 设置当前效果ID到线程本地变量
            // 这是一个巧妙的机制，用于将效果ID传递给ParticleProvider
            ClientPacketHandler.setCurrentEffectId(packet.effectId);
            
            try {
                // 生成粒子效果
                boolean success = BlizzardParticleManager.getInstance()
                        .spawnEffect(level, packet.effectId, packet.position);
                
                if (success) {
                    if (Config.enableDebugLogging) {
                        StormWeaver.LOGGER.debug("成功生成粒子效果: {} 在位置 {}", 
                                packet.effectId, packet.position);
                    }
                } else {
                    StormWeaver.LOGGER.warn("生成粒子效果失败: {}", packet.effectId);
                }
                
            } finally {
                // 清理线程本地变量
                ClientPacketHandler.clearCurrentEffectId();
            }
            
        } catch (Exception e) {
            StormWeaver.LOGGER.error("处理粒子效果数据包时发生错误", e);
        }
    }
    
    // Getters
    public ResourceLocation getEffectId() {
        return effectId;
    }
    
    public Vec3 getPosition() {
        return position;
    }
    
    public Vec3 getVelocity() {
        return velocity;
    }
    
    public String getExtraData() {
        return extraData;
    }
    
    @Override
    public String toString() {
        return "SpawnParticleEffectPacket{" +
                "effectId=" + effectId +
                ", position=" + position +
                ", velocity=" + velocity +
                ", extraData='" + extraData + '\'' +
                '}';
    }
}
