package com.stormweaver.stormweaver.network;

import net.minecraft.resources.ResourceLocation;
import net.minecraftforge.api.distmarker.Dist;
import net.minecraftforge.api.distmarker.OnlyIn;

/**
 * 客户端数据包处理器
 * 
 * 这个类提供了一个巧妙的机制来在网络数据包处理和粒子提供者之间传递上下文信息
 * 
 * 问题：Forge的ParticleProvider.createParticle()方法的标准签名中没有参数
 * 可以让我们直接传入解析好的BlizzardParticleEffect数据对象
 * 
 * 解决方案：使用ThreadLocal变量临时存储效果ID，然后在ParticleProvider中读取
 * 
 * 工作流程：
 * 1. 客户端接收网络数据包，包含效果ID和位置
 * 2. 在调用ClientLevel.addParticle()之前，将效果ID存储在ThreadLocal中
 * 3. 游戏引擎调用我们注册的ParticleProvider.createParticle()方法
 * 4. 在createParticle()方法中，从ThreadLocal读取效果ID
 * 5. 使用效果ID从缓存管理器中获取BlizzardParticleEffect对象
 * 6. 将数据对象传递给自定义粒子类的构造函数
 * 7. 清理ThreadLocal变量
 * 
// * <AUTHOR>
 */
@OnlyIn(Dist.CLIENT)
public class ClientPacketHandler {
    
    // 线程本地变量，用于存储当前正在处理的粒子效果ID
    private static final ThreadLocal<ResourceLocation> CURRENT_EFFECT_ID = new ThreadLocal<>();
    
    /**
     * 设置当前效果ID
     * 
     * 这个方法在网络数据包处理器中调用，在创建粒子之前
     * 
     * @param effectId 粒子效果ID
     */
    public static void setCurrentEffectId(ResourceLocation effectId) {
        CURRENT_EFFECT_ID.set(effectId);
    }
    
    /**
     * 获取当前效果ID
     * 
     * 这个方法在ParticleProvider.createParticle()中调用
     * 
     * @return 当前的粒子效果ID，如果没有设置则返回null
     */
    public static ResourceLocation getCurrentEffectId() {
        return CURRENT_EFFECT_ID.get();
    }
    
    /**
     * 清理当前效果ID
     * 
     * 这个方法在粒子创建完成后调用，清理ThreadLocal变量
     * 重要：必须调用这个方法以避免内存泄漏
     */
    public static void clearCurrentEffectId() {
        CURRENT_EFFECT_ID.remove();
    }
    
    /**
     * 检查是否有当前效果ID
     * 
     * @return 如果有当前效果ID返回true
     */
    public static boolean hasCurrentEffectId() {
        return CURRENT_EFFECT_ID.get() != null;
    }
}
