package com.stormweaver.stormweaver.network;

import com.stormweaver.stormweaver.StormWeaver;
import net.minecraft.resources.ResourceLocation;
import net.minecraftforge.network.NetworkRegistry;
import net.minecraftforge.network.simple.SimpleChannel;

/**
 * 网络处理器
 * 
 * 负责处理服务器和客户端之间的粒子效果同步
 * 由于粒子效果是纯客户端的视觉表现，服务器需要通过网络包
 * 告诉客户端在何时何地生成哪种粒子效果
 * 
 * <AUTHOR>
 */
public class NetworkHandler {
    
    // 网络协议版本
    private static final String PROTOCOL_VERSION = "1";
    
    // 网络通道
    public static final SimpleChannel INSTANCE = NetworkRegistry.newSimpleChannel(
        new ResourceLocation(StormWeaver.MODID, "main"),
        () -> PROTOCOL_VERSION,
        PROTOCOL_VERSION::equals,
        PROTOCOL_VERSION::equals
    );
    
    // 消息ID计数器
    private static int messageId = 0;
    
    /**
     * 注册网络消息
     */
    public static void register() {
        StormWeaver.LOGGER.info("注册网络消息处理器");
        
        // 注册生成粒子效果的消息
        INSTANCE.messageBuilder(SpawnParticleEffectPacket.class, messageId++)
                .decoder(SpawnParticleEffectPacket::decode)
                .encoder(SpawnParticleEffectPacket::encode)
                .consumerMainThread(SpawnParticleEffectPacket::handle)
                .add();
        
        StormWeaver.LOGGER.info("网络消息注册完成");
    }
    
    /**
     * 获取下一个消息ID
     * 
     * @return 消息ID
     */
    public static int nextMessageId() {
        return messageId++;
    }
}
