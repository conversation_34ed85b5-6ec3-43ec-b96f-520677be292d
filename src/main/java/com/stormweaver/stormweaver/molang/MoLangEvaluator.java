package com.stormweaver.stormweaver.molang;

import com.stormweaver.stormweaver.Config;
import com.stormweaver.stormweaver.StormWeaver;
import net.objecthunter.exp4j.Expression;
import net.objecthunter.exp4j.ExpressionBuilder;
import net.objecthunter.exp4j.function.Function;
import net.minecraft.world.phys.Vec3;
import net.minecraft.world.phys.Vec2;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * MoLang表达式求值引擎
 * 
 * 这个类是整个粒子系统的核心，负责：
 * 1. 解析和编译MoLang表达式字符串
 * 2. 管理表达式变量和函数
 * 3. 高性能的表达式求值
 * 4. 表达式缓存和优化
 * 
 * MoLang是Minecraft基岩版使用的数学表达式语言，支持：
 * - 基本数学运算
 * - 三角函数
 * - 插值函数
 * - 随机数生成
 * - 变量系统
 * 
 * <AUTHOR>
 */
public class MoLangEvaluator {
    
    // 表达式缓存，避免重复编译
    private static final Map<String, Expression> EXPRESSION_CACHE = new ConcurrentHashMap<>();
    
    // 当前实例的变量存储
    private final Map<String, Double> variables = new HashMap<>();
    
    // 当前实例的编译表达式缓存
    private final Map<String, Expression> instanceExpressions = new HashMap<>();
    
    // 随机数生成器
    private final java.util.Random random = new java.util.Random();
    
    /**
     * 构造函数，初始化默认变量和函数
     */
    public MoLangEvaluator() {
        initializeDefaultVariables();
    }
    
    /**
     * 初始化默认变量
     */
    private void initializeDefaultVariables() {
        // 粒子相关变量
        setVariable("variable.particle_age", 0.0);
        setVariable("variable.particle_lifetime", 1.0);
        setVariable("variable.particle_random_1", random.nextDouble());
        setVariable("variable.particle_random_2", random.nextDouble());
        setVariable("variable.particle_random_3", random.nextDouble());
        setVariable("variable.particle_random_4", random.nextDouble());
        
        // 发射器相关变量
        setVariable("variable.emitter_age", 0.0);
        setVariable("variable.emitter_lifetime", 1.0);
        setVariable("variable.emitter_random_1", random.nextDouble());
        setVariable("variable.emitter_random_2", random.nextDouble());
        setVariable("variable.emitter_random_3", random.nextDouble());
        setVariable("variable.emitter_random_4", random.nextDouble());
        
        // 实体相关变量
        setVariable("variable.entity_scale", 1.0);
    }
    
    /**
     * 设置变量值
     * 
     * @param name 变量名
     * @param value 变量值
     */
    public void setVariable(String name, double value) {
        // 将MoLang变量名转换为exp4j兼容的格式
        String exp4jName = convertVariableName(name);
        variables.put(exp4jName, value);
        
        // 更新已编译表达式中的变量
        for (Expression expr : instanceExpressions.values()) {
            try {
                expr.setVariable(exp4jName, value);
            } catch (Exception e) {
                // 忽略不存在的变量
            }
        }
    }
    
    /**
     * 获取变量值
     * 
     * @param name 变量名
     * @return 变量值，如果不存在则返回0.0
     */
    public double getVariable(String name) {
        String exp4jName = convertVariableName(name);
        return variables.getOrDefault(exp4jName, 0.0);
    }
    
    /**
     * 编译MoLang表达式
     * 
     * @param molangExpression MoLang表达式字符串
     * @param cacheKey 缓存键，用于标识这个表达式
     * @return 编译后的Expression对象
     */
    public Expression compileExpression(String molangExpression, String cacheKey) {
        if (molangExpression == null || molangExpression.trim().isEmpty()) {
            return createConstantExpression(0.0);
        }
        
        // 检查缓存
        if (Config.enableMolangOptimization && cacheKey != null) {
            Expression cached = EXPRESSION_CACHE.get(cacheKey);
            if (cached != null) {
                return cloneExpression(cached);
            }
        }
        
        try {
            // 预处理MoLang表达式
            String processedExpression = preprocessMoLangExpression(molangExpression);
            
            // 创建表达式构建器
            ExpressionBuilder builder = new ExpressionBuilder(processedExpression);
            
            // 添加自定义函数
            addCustomFunctions(builder);
            
            // 添加变量
            for (String varName : variables.keySet()) {
                builder.variable(varName);
            }
            
            // 构建表达式
            Expression expression = builder.build();
            
            // 设置变量值
            for (Map.Entry<String, Double> entry : variables.entrySet()) {
                expression.setVariable(entry.getKey(), entry.getValue());
            }
            
            // 缓存表达式
            if (Config.enableMolangOptimization && cacheKey != null) {
                if (EXPRESSION_CACHE.size() < Config.expressionCacheSize) {
                    EXPRESSION_CACHE.put(cacheKey, expression);
                }
            }
            
            return expression;
            
        } catch (Exception e) {
            StormWeaver.LOGGER.error("编译MoLang表达式失败: " + molangExpression, e);
            return createConstantExpression(0.0);
        }
    }
    
    /**
     * 求值MoLang表达式
     * 
     * @param molangExpression MoLang表达式字符串
     * @return 求值结果
     */
    public double evaluateExpression(String molangExpression) {
        return evaluateExpression(molangExpression, null);
    }
    
    /**
     * 求值MoLang表达式（带缓存）
     * 
     * @param molangExpression MoLang表达式字符串
     * @param cacheKey 缓存键
     * @return 求值结果
     */
    public double evaluateExpression(String molangExpression, String cacheKey) {
        if (molangExpression == null || molangExpression.trim().isEmpty()) {
            return 0.0;
        }
        
        // 尝试解析为常量
        try {
            return Double.parseDouble(molangExpression.trim());
        } catch (NumberFormatException e) {
            // 不是常量，继续处理
        }
        
        try {
            Expression expression = compileExpression(molangExpression, cacheKey);
            return expression.evaluate();
        } catch (Exception e) {
            StormWeaver.LOGGER.error("求值MoLang表达式失败: " + molangExpression, e);
            return 0.0;
        }
    }
    
    /**
     * 求值Vec3类型的表达式
     * 
     * @param xExpression X轴表达式
     * @param yExpression Y轴表达式
     * @param zExpression Z轴表达式
     * @return Vec3结果
     */
    public Vec3 evaluateVec3(String xExpression, String yExpression, String zExpression) {
        double x = evaluateExpression(xExpression);
        double y = evaluateExpression(yExpression);
        double z = evaluateExpression(zExpression);
        return new Vec3(x, y, z);
    }
    
    /**
     * 求值Vec2类型的表达式
     * 
     * @param xExpression X轴表达式
     * @param yExpression Y轴表达式
     * @return Vec2结果
     */
    public Vec2 evaluateVec2(String xExpression, String yExpression) {
        float x = (float) evaluateExpression(xExpression);
        float y = (float) evaluateExpression(yExpression);
        return new Vec2(x, y);
    }
    
    /**
     * 求值布尔表达式
     * 
     * @param expression 表达式
     * @return 布尔结果（非零为true）
     */
    public boolean evaluateBoolean(String expression) {
        return evaluateExpression(expression) != 0.0;
    }
    
    /**
     * 预处理MoLang表达式，转换为exp4j兼容格式
     * 
     * @param molangExpression 原始MoLang表达式
     * @return 处理后的表达式
     */
    private String preprocessMoLangExpression(String molangExpression) {
        String processed = molangExpression;
        
        // 替换变量名格式
        processed = processed.replaceAll("variable\\.", "variable_");
        processed = processed.replaceAll("query\\.", "query_");
        
        // 替换函数名格式
        processed = processed.replaceAll("math\\.", "math_");
        
        // 处理特殊语法
        processed = processed.replaceAll("Math\\.", "math_");
        
        return processed;
    }
    
    /**
     * 转换变量名为exp4j兼容格式
     * 
     * @param molangVariableName MoLang变量名
     * @return exp4j变量名
     */
    private String convertVariableName(String molangVariableName) {
        return molangVariableName.replaceAll("\\.", "_");
    }
    
    /**
     * 添加自定义函数到表达式构建器
     * 
     * @param builder 表达式构建器
     */
    private void addCustomFunctions(ExpressionBuilder builder) {
        // 三角函数（MoLang使用角度，需要转换）
        builder.function(new Function("math_sin", 1) {
            @Override
            public double apply(double... args) {
                return Math.sin(Math.toRadians(args[0]));
            }
        });
        
        builder.function(new Function("math_cos", 1) {
            @Override
            public double apply(double... args) {
                return Math.cos(Math.toRadians(args[0]));
            }
        });
        
        builder.function(new Function("math_tan", 1) {
            @Override
            public double apply(double... args) {
                return Math.tan(Math.toRadians(args[0]));
            }
        });
        
        // 线性插值函数
        builder.function(new Function("math_lerp", 3) {
            @Override
            public double apply(double... args) {
                double a = args[0];
                double b = args[1];
                double t = args[2];
                return a + t * (b - a);
            }
        });
        
        // 随机数函数
        builder.function(new Function("math_random", 2) {
            @Override
            public double apply(double... args) {
                double min = args[0];
                double max = args[1];
                return min + random.nextDouble() * (max - min);
            }
        });
        
        // 钳制函数
        builder.function(new Function("math_clamp", 3) {
            @Override
            public double apply(double... args) {
                double value = args[0];
                double min = args[1];
                double max = args[2];
                return Math.max(min, Math.min(max, value));
            }
        });
        
        // 最小值函数
        builder.function(new Function("math_min", 2) {
            @Override
            public double apply(double... args) {
                return Math.min(args[0], args[1]);
            }
        });
        
        // 最大值函数
        builder.function(new Function("math_max", 2) {
            @Override
            public double apply(double... args) {
                return Math.max(args[0], args[1]);
            }
        });
        
        // 绝对值函数
        builder.function(new Function("math_abs", 1) {
            @Override
            public double apply(double... args) {
                return Math.abs(args[0]);
            }
        });
        
        // 平方根函数
        builder.function(new Function("math_sqrt", 1) {
            @Override
            public double apply(double... args) {
                return Math.sqrt(args[0]);
            }
        });
        
        // 幂函数
        builder.function(new Function("math_pow", 2) {
            @Override
            public double apply(double... args) {
                return Math.pow(args[0], args[1]);
            }
        });
        
        // 向下取整
        builder.function(new Function("math_floor", 1) {
            @Override
            public double apply(double... args) {
                return Math.floor(args[0]);
            }
        });
        
        // 向上取整
        builder.function(new Function("math_ceil", 1) {
            @Override
            public double apply(double... args) {
                return Math.ceil(args[0]);
            }
        });
        
        // 四舍五入
        builder.function(new Function("math_round", 1) {
            @Override
            public double apply(double... args) {
                return Math.round(args[0]);
            }
        });
    }
    
    /**
     * 创建常量表达式
     * 
     * @param value 常量值
     * @return 表达式对象
     */
    private Expression createConstantExpression(double value) {
        return new ExpressionBuilder(String.valueOf(value)).build();
    }
    
    /**
     * 克隆表达式（用于缓存）
     * 
     * @param original 原始表达式
     * @return 克隆的表达式
     */
    private Expression cloneExpression(Expression original) {
        // exp4j的Expression不支持直接克隆，这里返回原始对象
        // 在实际使用中需要重新设置变量值
        return original;
    }
    
    /**
     * 清理缓存
     */
    public static void clearCache() {
        EXPRESSION_CACHE.clear();
    }
    
    /**
     * 获取缓存大小
     * 
     * @return 缓存中的表达式数量
     */
    public static int getCacheSize() {
        return EXPRESSION_CACHE.size();
    }
}
