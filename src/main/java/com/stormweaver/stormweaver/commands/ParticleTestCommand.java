package com.stormweaver.stormweaver.commands;

import com.mojang.brigadier.CommandDispatcher;
import com.mojang.brigadier.arguments.StringArgumentType;
import com.mojang.brigadier.context.CommandContext;
import com.stormweaver.stormweaver.network.NetworkHandler;
import com.stormweaver.stormweaver.network.SpawnParticleEffectPacket;
import net.minecraft.commands.CommandSourceStack;
import net.minecraft.commands.Commands;
import net.minecraft.network.chat.Component;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.world.phys.Vec3;
import net.minecraftforge.network.PacketDistributor;

/**
 * 粒子测试命令
 * 
 * 提供用于测试粒子效果的命令
 * 使用方法：/stormweaver particle <effect_id>
 * 
 * <AUTHOR>
 */
public class ParticleTestCommand {
    
    /**
     * 注册命令
     * 
     * @param dispatcher 命令分发器
     */
    public static void register(CommandDispatcher<CommandSourceStack> dispatcher) {
        dispatcher.register(
            Commands.literal("stormweaver")
                .requires(source -> source.hasPermission(2)) // 需要OP权限
                .then(Commands.literal("particle")
                    .then(Commands.argument("effect_id", StringArgumentType.string())
                        .executes(ParticleTestCommand::executeParticleCommand)
                    )
                )
                .then(Commands.literal("reload")
                    .executes(ParticleTestCommand::executeReloadCommand)
                )
                .then(Commands.literal("info")
                    .executes(ParticleTestCommand::executeInfoCommand)
                )
        );
    }
    
    /**
     * 执行粒子生成命令
     * 
     * @param context 命令上下文
     * @return 命令执行结果
     */
    private static int executeParticleCommand(CommandContext<CommandSourceStack> context) {
        try {
            CommandSourceStack source = context.getSource();
            String effectIdString = StringArgumentType.getString(context, "effect_id");
            
            // 解析效果ID
            ResourceLocation effectId;
            if (effectIdString.contains(":")) {
                effectId = new ResourceLocation(effectIdString);
            } else {
                effectId = new ResourceLocation("stormweaver", effectIdString);
            }
            
            // 获取玩家位置
            Vec3 position = source.getPosition();
            
            // 发送粒子效果数据包给附近的玩家
            SpawnParticleEffectPacket packet = new SpawnParticleEffectPacket(effectId, position);
            
            if (source.getEntity() instanceof ServerPlayer player) {
                // 发送给执行命令的玩家
                NetworkHandler.INSTANCE.send(PacketDistributor.PLAYER.with(() -> player), packet);
                
                // 也发送给附近的其他玩家
                NetworkHandler.INSTANCE.send(
                    PacketDistributor.NEAR.with(() -> new PacketDistributor.TargetPoint(
                        position.x, position.y, position.z, 64.0, player.level().dimension()
                    )), 
                    packet
                );
                
                source.sendSuccess(() -> Component.literal(
                    "已生成粒子效果: " + effectId + " 在位置 " + 
                    String.format("(%.1f, %.1f, %.1f)", position.x, position.y, position.z)
                ), true);
                
                return 1;
            } else {
                source.sendFailure(Component.literal("此命令只能由玩家执行"));
                return 0;
            }
            
        } catch (Exception e) {
            context.getSource().sendFailure(Component.literal("执行命令时发生错误: " + e.getMessage()));
            return 0;
        }
    }
    
    /**
     * 执行重载命令
     * 
     * @param context 命令上下文
     * @return 命令执行结果
     */
    private static int executeReloadCommand(CommandContext<CommandSourceStack> context) {
        try {
            CommandSourceStack source = context.getSource();
            
            // TODO: 实现重载功能
            // BlizzardParticleManager.getInstance().reload();
            
            source.sendSuccess(() -> Component.literal("粒子效果已重载"), true);
            return 1;
            
        } catch (Exception e) {
            context.getSource().sendFailure(Component.literal("重载时发生错误: " + e.getMessage()));
            return 0;
        }
    }
    
    /**
     * 执行信息命令
     * 
     * @param context 命令上下文
     * @return 命令执行结果
     */
    private static int executeInfoCommand(CommandContext<CommandSourceStack> context) {
        try {
            CommandSourceStack source = context.getSource();
            
            // TODO: 实现信息显示功能
            // BlizzardParticleManager manager = BlizzardParticleManager.getInstance();
            
            source.sendSuccess(() -> Component.literal(
                "StormWeaver 粒子系统信息:\n" +
                "- 版本: 1.0.0\n" +
                "- 状态: 运行中\n" +
                "- 已加载效果: 待实现\n" +
                "- 活跃发射器: 待实现"
            ), false);
            
            return 1;
            
        } catch (Exception e) {
            context.getSource().sendFailure(Component.literal("获取信息时发生错误: " + e.getMessage()));
            return 0;
        }
    }
}
