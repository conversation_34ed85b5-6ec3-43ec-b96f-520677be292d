package com.stormweaver.stormweaver.particle;

import com.stormweaver.stormweaver.Config;
import com.stormweaver.stormweaver.StormWeaver;
import com.stormweaver.stormweaver.data.BlizzardParticleEffect;
import com.stormweaver.stormweaver.data.components.ParticleComponents;
import com.stormweaver.stormweaver.data.components.particle.ParticleMotionDynamic;
import com.stormweaver.stormweaver.data.components.particle.ParticleAppearanceBillboard;
import com.stormweaver.stormweaver.data.components.particle.ParticleLifetimeExpression;
import com.stormweaver.stormweaver.molang.MoLangEvaluator;
import net.minecraft.client.multiplayer.ClientLevel;
import net.minecraft.client.particle.Particle;
import net.minecraft.client.particle.ParticleRenderType;
import net.minecraft.client.particle.SpriteSet;
import net.minecraft.world.phys.Vec3;
import net.minecraftforge.api.distmarker.Dist;
import net.minecraftforge.api.distmarker.OnlyIn;

/**
 * 暴雪粒子类
 * 
 * 这是自定义粒子类的核心实现，继承自Particle基类
 * 每个BlizzardParticle实例都是一个自包含的状态机，其行为完全由
 * 传入的BlizzardParticleEffect数据对象决定
 * 
 * 核心功能：
 * 1. 在构造函数中编译所有MoLang表达式
 * 2. 在tick()方法中执行"更新-求值-应用"循环
 * 3. 处理粒子的物理运动、视觉外观和生命周期
 * 4. 支持自定义渲染（动态纹理）
 * 
 * 设计原则：
 * - 一次编译，多次求值：避免每帧重复编译表达式
 * - 模块化组件处理：根据JSON中存在的组件进行相应处理
 * - 性能优化：缓存计算结果，避免不必要的重复计算
 * 
 * <AUTHOR>
 */
@OnlyIn(Dist.CLIENT)
public class BlizzardParticle extends Particle {
    
    // 粒子效果数据
    private final BlizzardParticleEffect effectData;
    
    // MoLang求值器
    private final MoLangEvaluator moLangEvaluator;
    
    // 精灵集合（虽然我们使用自定义渲染，但保留以备将来使用）
    private final SpriteSet spriteSet;
    
    // 粒子组件缓存
    private final ParticleComponents components;
    
    // 运动相关
    private ParticleMotionDynamic motionDynamic;
    
    // 外观相关
    private ParticleAppearanceBillboard appearanceBillboard;
    
    // 生命周期相关
    private ParticleLifetimeExpression lifetimeExpression;
    
    // 性能优化：缓存常用的表达式结果
    private boolean hasMotionDynamic = false;
    private boolean hasAppearanceBillboard = false;
    private boolean hasLifetimeExpression = false;
    
    // 旋转相关
    private float rotation = 0.0f;
    private float rotationSpeed = 0.0f;
    
    /**
     * 构造函数
     * 
     * @param level 客户端世界
     * @param x X坐标
     * @param y Y坐标
     * @param z Z坐标
     * @param xd X方向速度
     * @param yd Y方向速度
     * @param zd Z方向速度
     * @param effectData 粒子效果数据
     * @param spriteSet 精灵集合
     */
    public BlizzardParticle(ClientLevel level, double x, double y, double z,
                           double xd, double yd, double zd,
                           BlizzardParticleEffect effectData, SpriteSet spriteSet) {
        super(level, x, y, z, xd, yd, zd);
        
        this.effectData = effectData;
        this.spriteSet = spriteSet;
        this.moLangEvaluator = new MoLangEvaluator();
        
        // 获取组件数据
        this.components = effectData.getParticleEffect().getComponents();
        
        if (this.components != null) {
            // 缓存组件引用
            this.motionDynamic = components.getParticleMotionDynamic();
            this.appearanceBillboard = components.getParticleAppearanceBillboard();
            this.lifetimeExpression = components.getParticleLifetimeExpression();
            
            // 设置组件存在标志
            this.hasMotionDynamic = (motionDynamic != null);
            this.hasAppearanceBillboard = (appearanceBillboard != null);
            this.hasLifetimeExpression = (lifetimeExpression != null);
        }
        
        // 初始化粒子属性
        initializeParticle();
        
        if (Config.enableDebugLogging) {
            StormWeaver.LOGGER.debug("创建暴雪粒子: {}, 组件 - 运动:{}, 外观:{}, 生命周期:{}", 
                    effectData.getIdentifier(), hasMotionDynamic, hasAppearanceBillboard, hasLifetimeExpression);
        }
    }
    
    /**
     * 初始化粒子属性
     */
    private void initializeParticle() {
        try {
            // 设置默认生命周期
            this.lifetime = 100; // 默认5秒（20 ticks/秒）
            
            // 处理生命周期组件
            if (hasLifetimeExpression && lifetimeExpression != null) {
                String maxLifetimeExpr = lifetimeExpression.getMaxLifetime();
                if (maxLifetimeExpr != null && !maxLifetimeExpr.trim().isEmpty()) {
                    double lifetimeSeconds = moLangEvaluator.evaluateExpression(maxLifetimeExpr);
                    this.lifetime = (int) (lifetimeSeconds * 20); // 转换为ticks
                }
            }
            
            // 设置默认外观属性
            this.quadSize = 0.1f;
            this.setColor(1.0f, 1.0f, 1.0f); // 白色
            this.setAlpha(1.0f); // 完全不透明
            
            // 处理外观组件
            if (hasAppearanceBillboard && appearanceBillboard != null) {
                String[] sizeExpr = appearanceBillboard.getSize();
                if (sizeExpr != null && sizeExpr.length >= 2) {
                    double width = moLangEvaluator.evaluateExpression(sizeExpr[0]);
                    double height = moLangEvaluator.evaluateExpression(sizeExpr[1]);
                    this.quadSize = (float) ((width + height) / 2.0); // 简化处理，取平均值
                }
            }
            
            // 初始化MoLang变量
            updateMoLangVariables();
            
        } catch (Exception e) {
            StormWeaver.LOGGER.error("初始化暴雪粒子时发生错误", e);
            // 设置安全的默认值
            this.lifetime = 100;
            this.quadSize = 0.1f;
            this.setColor(1.0f, 1.0f, 1.0f);
            this.setAlpha(1.0f);
        }
    }
    
    /**
     * 更新MoLang变量
     */
    private void updateMoLangVariables() {
        // 更新粒子年龄相关变量
        float particleAge = this.age / 20.0f; // 转换为秒
        float particleLifetime = this.lifetime / 20.0f; // 转换为秒
        
        moLangEvaluator.setVariable("variable.particle_age", particleAge);
        moLangEvaluator.setVariable("variable.particle_lifetime", particleLifetime);
        
        // 计算生命周期比例
        float lifeRatio = particleLifetime > 0 ? particleAge / particleLifetime : 0.0f;
        moLangEvaluator.setVariable("variable.particle_life_ratio", lifeRatio);
    }
    
    /**
     * 粒子tick更新
     * 这是粒子行为的驱动核心，每帧调用一次
     */
    @Override
    public void tick() {
        // 调用父类方法处理基础物理和年龄增长
        super.tick();
        
        // 检查生命周期
        if (this.age >= this.lifetime) {
            this.remove();
            return;
        }
        
        try {
            // 1. 更新MoLang上下文
            updateMoLangVariables();
            
            // 2. 处理动态运动
            if (hasMotionDynamic) {
                processMotionDynamic();
            }
            
            // 3. 处理外观变化
            if (hasAppearanceBillboard) {
                processAppearanceBillboard();
            }
            
            // 4. 处理生命周期表达式
            if (hasLifetimeExpression) {
                processLifetimeExpression();
            }
            
            // 5. 更新旋转
            updateRotation();
            
        } catch (Exception e) {
            StormWeaver.LOGGER.error("更新暴雪粒子时发生错误", e);
            // 发生错误时移除粒子以避免进一步问题
            this.remove();
        }
    }
    
    /**
     * 处理动态运动组件
     */
    private void processMotionDynamic() {
        if (motionDynamic == null) return;
        
        try {
            // 处理线性加速度
            String[] accelExpr = motionDynamic.getLinearAcceleration();
            if (accelExpr != null && accelExpr.length >= 3) {
                double ax = moLangEvaluator.evaluateExpression(accelExpr[0]);
                double ay = moLangEvaluator.evaluateExpression(accelExpr[1]);
                double az = moLangEvaluator.evaluateExpression(accelExpr[2]);
                
                // 应用加速度（每tick）
                this.xd += ax / 20.0; // 转换为每tick的加速度
                this.yd += ay / 20.0;
                this.zd += az / 20.0;
            }
            
            // 处理线性阻力
            String dragExpr = motionDynamic.getLinearDragCoefficient();
            if (dragExpr != null && !dragExpr.trim().isEmpty()) {
                double drag = moLangEvaluator.evaluateExpression(dragExpr);
                if (drag > 0) {
                    double dragFactor = 1.0 - (drag / 20.0); // 转换为每tick的阻力
                    dragFactor = Math.max(0.0, dragFactor); // 确保不为负数
                    
                    this.xd *= dragFactor;
                    this.yd *= dragFactor;
                    this.zd *= dragFactor;
                }
            }
            
            // 处理旋转加速度
            String rotAccelExpr = motionDynamic.getRotationAcceleration();
            if (rotAccelExpr != null && !rotAccelExpr.trim().isEmpty()) {
                double rotAccel = moLangEvaluator.evaluateExpression(rotAccelExpr);
                this.rotationSpeed += (float) (rotAccel / 20.0); // 度/tick
            }
            
            // 处理旋转阻力
            String rotDragExpr = motionDynamic.getRotationDragCoefficient();
            if (rotDragExpr != null && !rotDragExpr.trim().isEmpty()) {
                double rotDrag = moLangEvaluator.evaluateExpression(rotDragExpr);
                if (rotDrag > 0) {
                    this.rotationSpeed *= (1.0f - (float) (rotDrag / 20.0));
                }
            }
            
        } catch (Exception e) {
            StormWeaver.LOGGER.error("处理粒子动态运动时发生错误", e);
        }
    }
    
    /**
     * 处理外观广告牌组件
     */
    private void processAppearanceBillboard() {
        if (appearanceBillboard == null) return;
        
        try {
            // 处理尺寸变化
            String[] sizeExpr = appearanceBillboard.getSize();
            if (sizeExpr != null && sizeExpr.length >= 2) {
                double width = moLangEvaluator.evaluateExpression(sizeExpr[0]);
                double height = moLangEvaluator.evaluateExpression(sizeExpr[1]);
                this.quadSize = (float) ((width + height) / 2.0); // 简化处理
            }
            
            // TODO: 处理UV坐标、翻页动画等
            // 这些功能需要自定义渲染器的支持
            
        } catch (Exception e) {
            StormWeaver.LOGGER.error("处理粒子外观时发生错误", e);
        }
    }
    
    /**
     * 处理生命周期表达式组件
     */
    private void processLifetimeExpression() {
        if (lifetimeExpression == null) return;
        
        try {
            // 处理过期表达式
            String expirationExpr = lifetimeExpression.getExpirationExpression();
            if (expirationExpr != null && !expirationExpr.trim().isEmpty()) {
                boolean shouldExpire = moLangEvaluator.evaluateBoolean(expirationExpr);
                if (shouldExpire) {
                    this.remove();
                }
            }
            
        } catch (Exception e) {
            StormWeaver.LOGGER.error("处理粒子生命周期时发生错误", e);
        }
    }
    
    /**
     * 更新旋转
     */
    private void updateRotation() {
        this.rotation += this.rotationSpeed;
        
        // 保持旋转角度在合理范围内
        while (this.rotation > 360.0f) {
            this.rotation -= 360.0f;
        }
        while (this.rotation < 0.0f) {
            this.rotation += 360.0f;
        }
    }
    
    /**
     * 获取渲染类型
     * 
     * @return 粒子渲染类型
     */
    @Override
    public ParticleRenderType getRenderType() {
        // 使用半透明渲染类型以支持透明度
        return ParticleRenderType.PARTICLE_SHEET_TRANSLUCENT;
    }
    
    /**
     * 获取粒子效果数据
     * 
     * @return 粒子效果数据
     */
    public BlizzardParticleEffect getEffectData() {
        return effectData;
    }
    
    /**
     * 获取MoLang求值器
     * 
     * @return MoLang求值器
     */
    public MoLangEvaluator getMoLangEvaluator() {
        return moLangEvaluator;
    }
    
    /**
     * 获取当前旋转角度
     * 
     * @return 旋转角度（度）
     */
    public float getRotation() {
        return rotation;
    }
    
    /**
     * 获取旋转速度
     * 
     * @return 旋转速度（度/tick）
     */
    public float getRotationSpeed() {
        return rotationSpeed;
    }
}
