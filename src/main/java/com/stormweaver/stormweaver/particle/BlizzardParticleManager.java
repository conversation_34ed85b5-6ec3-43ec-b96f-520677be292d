package com.stormweaver.stormweaver.particle;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.stormweaver.stormweaver.Config;
import com.stormweaver.stormweaver.StormWeaver;
import com.stormweaver.stormweaver.data.BlizzardParticleEffect;
import net.minecraft.client.Minecraft;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.server.packs.resources.Resource;
import net.minecraft.server.packs.resources.ResourceManager;
import net.minecraft.world.level.Level;
import net.minecraft.world.phys.Vec3;
import net.minecraftforge.api.distmarker.Dist;
import net.minecraftforge.api.distmarker.OnlyIn;
import net.minecraftforge.client.event.ClientTickEvent;
import net.minecraftforge.eventbus.api.SubscribeEvent;
import net.minecraftforge.fml.common.Mod;

import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 暴雪粒子效果管理器
 * 
 * 这个类是整个粒子系统的中心枢纽，负责：
 * 1. 加载和解析基岩版粒子JSON文件
 * 2. 缓存解析后的粒子效果数据
 * 3. 管理活跃的粒子发射器
 * 4. 提供公共API供其他模组使用
 * 5. 处理客户端tick事件
 * 
 * 采用单例模式确保全局唯一性。
 * 
 * <AUTHOR>
 */
@OnlyIn(Dist.CLIENT)
@Mod.EventBusSubscriber(modid = StormWeaver.MODID, bus = Mod.EventBusSubscriber.Bus.FORGE, value = Dist.CLIENT)
public class BlizzardParticleManager {
    
    // 单例实例
    private static BlizzardParticleManager instance;
    
    // Gson解析器
    private final Gson gson;
    
    // 粒子效果缓存 - ResourceLocation -> BlizzardParticleEffect
    private final Map<ResourceLocation, BlizzardParticleEffect> effectCache = new ConcurrentHashMap<>();
    
    // 活跃的粒子发射器列表
    private final List<BlizzardParticleEmitter> activeEmitters = Collections.synchronizedList(new ArrayList<>());
    
    // 待移除的发射器列表（避免在迭代时修改集合）
    private final List<BlizzardParticleEmitter> emittersToRemove = Collections.synchronizedList(new ArrayList<>());
    
    // 性能监控
    private long lastPerformanceCheck = 0;
    private int particleCount = 0;
    private int emitterCount = 0;
    
    /**
     * 私有构造函数，实现单例模式
     */
    private BlizzardParticleManager() {
        this.gson = new GsonBuilder()
                .setPrettyPrinting()
                .setLenient()
                .create();
    }
    
    /**
     * 获取单例实例
     * 
     * @return BlizzardParticleManager实例
     */
    public static BlizzardParticleManager getInstance() {
        if (instance == null) {
            instance = new BlizzardParticleManager();
        }
        return instance;
    }
    
    /**
     * 初始化管理器
     * 在客户端设置阶段调用
     */
    public void initialize() {
        StormWeaver.LOGGER.info("初始化暴雪粒子管理器");
        
        // 加载所有粒子效果
        loadAllParticleEffects();
        
        StormWeaver.LOGGER.info("暴雪粒子管理器初始化完成，加载了 {} 个粒子效果", effectCache.size());
    }
    
    /**
     * 加载所有粒子效果文件
     */
    private void loadAllParticleEffects() {
        if (!Config.enableResourceCaching) {
            StormWeaver.LOGGER.info("资源缓存已禁用，跳过预加载");
            return;
        }
        
        try {
            Minecraft minecraft = Minecraft.getInstance();
            if (minecraft == null) {
                StormWeaver.LOGGER.warn("Minecraft实例为null，无法加载资源");
                return;
            }
            
            ResourceManager resourceManager = minecraft.getResourceManager();
            
            // 搜索所有blizzard_particles目录下的JSON文件
            String basePath = "blizzard_particles";
            
            // 获取所有可用的命名空间
            Set<String> namespaces = resourceManager.getNamespaces();
            
            for (String namespace : namespaces) {
                try {
                    // 尝试获取该命名空间下的粒子文件
                    loadParticleEffectsFromNamespace(resourceManager, namespace, basePath);
                } catch (Exception e) {
                    StormWeaver.LOGGER.debug("命名空间 {} 中没有找到粒子文件: {}", namespace, e.getMessage());
                }
            }
            
        } catch (Exception e) {
            StormWeaver.LOGGER.error("加载粒子效果时发生错误", e);
        }
    }
    
    /**
     * 从指定命名空间加载粒子效果
     * 
     * @param resourceManager 资源管理器
     * @param namespace 命名空间
     * @param basePath 基础路径
     */
    private void loadParticleEffectsFromNamespace(ResourceManager resourceManager, String namespace, String basePath) {
        try {
            // 构建资源位置
            ResourceLocation baseLocation = new ResourceLocation(namespace, basePath);
            
            // 尝试获取目录下的所有资源
            Map<ResourceLocation, Resource> resources = resourceManager.listResources(basePath, 
                location -> location.getPath().endsWith(".json"));
            
            for (Map.Entry<ResourceLocation, Resource> entry : resources.entrySet()) {
                ResourceLocation location = entry.getKey();
                Resource resource = entry.getValue();
                
                try {
                    loadParticleEffect(location, resource);
                } catch (Exception e) {
                    StormWeaver.LOGGER.error("加载粒子效果失败: {}", location, e);
                }
            }
            
        } catch (Exception e) {
            StormWeaver.LOGGER.debug("无法访问命名空间 {} 的资源: {}", namespace, e.getMessage());
        }
    }
    
    /**
     * 加载单个粒子效果文件
     * 
     * @param location 资源位置
     * @param resource 资源对象
     */
    private void loadParticleEffect(ResourceLocation location, Resource resource) {
        try (InputStreamReader reader = new InputStreamReader(resource.open(), StandardCharsets.UTF_8)) {
            
            BlizzardParticleEffect effect = gson.fromJson(reader, BlizzardParticleEffect.class);
            
            if (effect == null) {
                StormWeaver.LOGGER.warn("解析粒子效果失败，结果为null: {}", location);
                return;
            }
            
            if (!effect.isValid()) {
                StormWeaver.LOGGER.warn("粒子效果数据无效: {}", location);
                return;
            }
            
            // 将文件路径转换为效果ID
            ResourceLocation effectId = createEffectId(location);
            
            // 缓存效果
            effectCache.put(effectId, effect);
            
            if (Config.enableDebugLogging) {
                StormWeaver.LOGGER.debug("成功加载粒子效果: {} -> {}", effectId, effect.getIdentifier());
            }
            
        } catch (IOException e) {
            StormWeaver.LOGGER.error("读取粒子效果文件失败: {}", location, e);
        } catch (Exception e) {
            StormWeaver.LOGGER.error("解析粒子效果JSON失败: {}", location, e);
        }
    }
    
    /**
     * 从文件路径创建效果ID
     * 
     * @param fileLocation 文件位置
     * @return 效果ID
     */
    private ResourceLocation createEffectId(ResourceLocation fileLocation) {
        String path = fileLocation.getPath();
        
        // 移除blizzard_particles前缀和.json后缀
        if (path.startsWith("blizzard_particles/")) {
            path = path.substring("blizzard_particles/".length());
        }
        
        if (path.endsWith(".json")) {
            path = path.substring(0, path.length() - ".json".length());
        }
        
        return new ResourceLocation(fileLocation.getNamespace(), path);
    }
    
    /**
     * 获取粒子效果数据
     * 
     * @param effectId 效果ID
     * @return 粒子效果数据，如果不存在则返回null
     */
    public BlizzardParticleEffect getEffect(ResourceLocation effectId) {
        BlizzardParticleEffect effect = effectCache.get(effectId);
        
        if (effect == null && !effectCache.containsKey(effectId)) {
            // 尝试动态加载
            effect = loadEffectDynamically(effectId);
        }
        
        return effect;
    }
    
    /**
     * 动态加载粒子效果
     * 
     * @param effectId 效果ID
     * @return 粒子效果数据，如果加载失败则返回null
     */
    private BlizzardParticleEffect loadEffectDynamically(ResourceLocation effectId) {
        try {
            Minecraft minecraft = Minecraft.getInstance();
            if (minecraft == null) {
                return null;
            }
            
            ResourceManager resourceManager = minecraft.getResourceManager();
            
            // 构建文件路径
            ResourceLocation fileLocation = new ResourceLocation(
                effectId.getNamespace(), 
                "blizzard_particles/" + effectId.getPath() + ".json"
            );
            
            Optional<Resource> resourceOpt = resourceManager.getResource(fileLocation);
            if (resourceOpt.isPresent()) {
                loadParticleEffect(fileLocation, resourceOpt.get());
                return effectCache.get(effectId);
            }
            
        } catch (Exception e) {
            StormWeaver.LOGGER.error("动态加载粒子效果失败: {}", effectId, e);
        }
        
        return null;
    }
    
    /**
     * 生成粒子效果
     * 
     * @param level 世界
     * @param effectId 效果ID
     * @param position 生成位置
     * @return 是否成功生成
     */
    public boolean spawnEffect(Level level, ResourceLocation effectId, Vec3 position) {
        if (level == null || !level.isClientSide) {
            return false;
        }
        
        BlizzardParticleEffect effect = getEffect(effectId);
        if (effect == null) {
            StormWeaver.LOGGER.warn("未找到粒子效果: {}", effectId);
            return false;
        }
        
        try {
            // 检查发射器数量限制
            if (activeEmitters.size() >= Config.maxActiveEmitters) {
                // 移除最旧的发射器
                if (!activeEmitters.isEmpty()) {
                    BlizzardParticleEmitter oldest = activeEmitters.get(0);
                    oldest.expire();
                    activeEmitters.remove(0);
                }
            }
            
            // 创建新的发射器
            BlizzardParticleEmitter emitter = new BlizzardParticleEmitter(level, position, effect);
            activeEmitters.add(emitter);
            
            if (Config.enableDebugLogging) {
                StormWeaver.LOGGER.debug("生成粒子效果: {} 在位置 {}", effectId, position);
            }
            
            return true;
            
        } catch (Exception e) {
            StormWeaver.LOGGER.error("生成粒子效果时发生错误: {}", effectId, e);
            return false;
        }
    }
    
    /**
     * 客户端tick事件处理
     * 更新所有活跃的发射器
     */
    @SubscribeEvent
    public static void onClientTick(ClientTickEvent.Pre event) {
        getInstance().tick();
    }
    
    /**
     * 执行tick更新
     */
    private void tick() {
        if (activeEmitters.isEmpty()) {
            return;
        }
        
        // 更新所有发射器
        emittersToRemove.clear();
        
        for (BlizzardParticleEmitter emitter : activeEmitters) {
            try {
                emitter.tick();
                
                // 检查发射器是否应该被移除
                if (emitter.shouldRemove()) {
                    emittersToRemove.add(emitter);
                }
                
            } catch (Exception e) {
                StormWeaver.LOGGER.error("更新粒子发射器时发生错误", e);
                emittersToRemove.add(emitter);
            }
        }
        
        // 移除过期的发射器
        if (!emittersToRemove.isEmpty()) {
            activeEmitters.removeAll(emittersToRemove);
            
            if (Config.enableDebugLogging) {
                StormWeaver.LOGGER.debug("移除了 {} 个过期的粒子发射器", emittersToRemove.size());
            }
        }
        
        // 性能监控
        if (Config.enablePerformanceMonitoring) {
            updatePerformanceMetrics();
        }
    }
    
    /**
     * 更新性能指标
     */
    private void updatePerformanceMetrics() {
        long currentTime = System.currentTimeMillis();
        
        if (currentTime - lastPerformanceCheck > 5000) { // 每5秒检查一次
            emitterCount = activeEmitters.size();
            particleCount = activeEmitters.stream()
                    .mapToInt(BlizzardParticleEmitter::getParticleCount)
                    .sum();
            
            if (Config.showParticleDebugInfo) {
                StormWeaver.LOGGER.info("粒子系统状态 - 发射器: {}, 粒子: {}, 缓存: {}", 
                        emitterCount, particleCount, effectCache.size());
            }
            
            lastPerformanceCheck = currentTime;
        }
    }
    
    /**
     * 获取活跃发射器数量
     * 
     * @return 发射器数量
     */
    public int getActiveEmitterCount() {
        return activeEmitters.size();
    }
    
    /**
     * 获取总粒子数量
     * 
     * @return 粒子数量
     */
    public int getTotalParticleCount() {
        return activeEmitters.stream()
                .mapToInt(BlizzardParticleEmitter::getParticleCount)
                .sum();
    }
    
    /**
     * 获取缓存的效果数量
     * 
     * @return 效果数量
     */
    public int getCachedEffectCount() {
        return effectCache.size();
    }
    
    /**
     * 清理所有资源
     */
    public void cleanup() {
        activeEmitters.clear();
        emittersToRemove.clear();
        
        if (!Config.enableResourceCaching) {
            effectCache.clear();
        }
        
        StormWeaver.LOGGER.info("暴雪粒子管理器已清理");
    }
}
