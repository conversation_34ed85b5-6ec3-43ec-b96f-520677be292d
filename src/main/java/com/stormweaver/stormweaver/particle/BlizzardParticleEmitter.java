package com.stormweaver.stormweaver.particle;

import com.stormweaver.stormweaver.Config;
import com.stormweaver.stormweaver.StormWeaver;
import com.stormweaver.stormweaver.data.BlizzardParticleEffect;
import com.stormweaver.stormweaver.data.components.ParticleComponents;
import com.stormweaver.stormweaver.data.components.emitter.EmitterRateSteady;
import com.stormweaver.stormweaver.data.components.emitter.EmitterShapeSphere;
import com.stormweaver.stormweaver.data.components.emitter.EmitterLifetimeOnce;
import com.stormweaver.stormweaver.molang.MoLangEvaluator;
import net.minecraft.client.Minecraft;
import net.minecraft.world.level.Level;
import net.minecraft.world.phys.Vec3;
import net.minecraftforge.api.distmarker.Dist;
import net.minecraftforge.api.distmarker.OnlyIn;

import java.util.ArrayList;
import java.util.List;
import java.util.Random;

/**
 * 暴雪粒子发射器
 * 
 * 发射器负责管理粒子的生成过程，与单个粒子分离
 * 一个发射器可以持续生成多个粒子，每个粒子都有独立的生命周期
 * 
 * 发射器的职责：
 * 1. 管理自身的生命周期（一次性、循环等）
 * 2. 根据速率组件决定何时生成新粒子
 * 3. 根据形状组件决定粒子的初始位置和方向
 * 4. 跟踪活跃粒子数量，遵守最大粒子限制
 * 
 * 这种架构精确模拟了基岩版中发射器和粒子的职责分离
 * 
 * <AUTHOR>
 */
@OnlyIn(Dist.CLIENT)
public class BlizzardParticleEmitter {
    
    // 发射器所在的世界
    private final Level level;
    
    // 发射器位置
    private final Vec3 position;
    
    // 粒子效果数据
    private final BlizzardParticleEffect effectData;
    
    // MoLang求值器
    private final MoLangEvaluator moLangEvaluator;
    
    // 随机数生成器
    private final Random random;
    
    // 发射器组件
    private final ParticleComponents components;
    
    // 发射器状态
    private int age = 0;
    private int lifetime = -1; // -1表示无限生命周期
    private boolean shouldRemove = false;
    private boolean isActive = true;
    
    // 粒子生成相关
    private float particleAccumulator = 0.0f; // 用于处理非整数生成速率
    private final List<BlizzardParticle> activeParticles = new ArrayList<>();
    
    // 组件缓存
    private EmitterRateSteady rateSteady;
    private EmitterShapeSphere shapeSphere;
    private EmitterLifetimeOnce lifetimeOnce;
    
    // 性能优化标志
    private boolean hasRateSteady = false;
    private boolean hasShapeSphere = false;
    private boolean hasLifetimeOnce = false;
    
    /**
     * 构造函数
     * 
     * @param level 世界
     * @param position 发射器位置
     * @param effectData 粒子效果数据
     */
    public BlizzardParticleEmitter(Level level, Vec3 position, BlizzardParticleEffect effectData) {
        this.level = level;
        this.position = position;
        this.effectData = effectData;
        this.moLangEvaluator = new MoLangEvaluator();
        this.random = new Random();
        
        // 获取组件数据
        this.components = effectData.getParticleEffect().getComponents();
        
        if (this.components != null) {
            // 缓存组件引用
            this.rateSteady = components.getEmitterRateSteady();
            this.shapeSphere = components.getEmitterShapeSphere();
            this.lifetimeOnce = components.getEmitterLifetimeOnce();
            
            // 设置组件存在标志
            this.hasRateSteady = (rateSteady != null);
            this.hasShapeSphere = (shapeSphere != null);
            this.hasLifetimeOnce = (lifetimeOnce != null);
        }
        
        // 初始化发射器
        initializeEmitter();
        
        if (Config.enableDebugLogging) {
            StormWeaver.LOGGER.debug("创建粒子发射器: {}, 位置: {}, 组件 - 速率:{}, 形状:{}, 生命周期:{}", 
                    effectData.getIdentifier(), position, hasRateSteady, hasShapeSphere, hasLifetimeOnce);
        }
    }
    
    /**
     * 初始化发射器属性
     */
    private void initializeEmitter() {
        try {
            // 处理生命周期组件
            if (hasLifetimeOnce && lifetimeOnce != null) {
                String activeTimeExpr = lifetimeOnce.getActiveTime();
                if (activeTimeExpr != null && !activeTimeExpr.trim().isEmpty()) {
                    double activeTimeSeconds = moLangEvaluator.evaluateExpression(activeTimeExpr);
                    this.lifetime = (int) (activeTimeSeconds * 20); // 转换为ticks
                }
            }
            
            // 初始化MoLang变量
            updateMoLangVariables();
            
        } catch (Exception e) {
            StormWeaver.LOGGER.error("初始化粒子发射器时发生错误", e);
            // 设置安全的默认值
            this.lifetime = 100; // 5秒
        }
    }
    
    /**
     * 更新MoLang变量
     */
    private void updateMoLangVariables() {
        // 更新发射器年龄相关变量
        float emitterAge = this.age / 20.0f; // 转换为秒
        float emitterLifetime = this.lifetime > 0 ? this.lifetime / 20.0f : Float.MAX_VALUE;
        
        moLangEvaluator.setVariable("variable.emitter_age", emitterAge);
        moLangEvaluator.setVariable("variable.emitter_lifetime", emitterLifetime);
        
        // 设置发射器随机变量（在发射器生命周期内保持不变）
        if (age == 0) {
            moLangEvaluator.setVariable("variable.emitter_random_1", random.nextDouble());
            moLangEvaluator.setVariable("variable.emitter_random_2", random.nextDouble());
            moLangEvaluator.setVariable("variable.emitter_random_3", random.nextDouble());
            moLangEvaluator.setVariable("variable.emitter_random_4", random.nextDouble());
        }
    }
    
    /**
     * 发射器tick更新
     */
    public void tick() {
        if (shouldRemove || !isActive) {
            return;
        }
        
        try {
            // 增加年龄
            age++;
            
            // 检查生命周期
            if (lifetime > 0 && age >= lifetime) {
                expire();
                return;
            }
            
            // 更新MoLang上下文
            updateMoLangVariables();
            
            // 清理已死亡的粒子
            cleanupDeadParticles();
            
            // 生成新粒子
            if (hasRateSteady) {
                generateParticlesFromRateSteady();
            }
            
        } catch (Exception e) {
            StormWeaver.LOGGER.error("更新粒子发射器时发生错误", e);
            expire();
        }
    }
    
    /**
     * 清理已死亡的粒子
     */
    private void cleanupDeadParticles() {
        activeParticles.removeIf(particle -> !particle.isAlive());
    }
    
    /**
     * 根据稳定速率组件生成粒子
     */
    private void generateParticlesFromRateSteady() {
        if (rateSteady == null) return;
        
        try {
            // 获取生成速率（粒子/秒）
            String spawnRateExpr = rateSteady.getSpawnRate();
            double spawnRate = moLangEvaluator.evaluateExpression(spawnRateExpr);
            
            // 获取最大粒子数
            String maxParticlesExpr = rateSteady.getMaxParticles();
            int maxParticles = (int) moLangEvaluator.evaluateExpression(maxParticlesExpr);
            
            // 应用配置限制
            maxParticles = Math.min(maxParticles, Config.maxParticlesPerEmitter);
            
            // 检查是否已达到最大粒子数
            if (activeParticles.size() >= maxParticles) {
                return;
            }
            
            // 计算这一tick应该生成的粒子数
            float particlesToSpawn = (float) (spawnRate / 20.0); // 转换为每tick
            particleAccumulator += particlesToSpawn;
            
            // 生成整数个粒子
            int particlesToSpawnNow = (int) particleAccumulator;
            particleAccumulator -= particlesToSpawnNow;
            
            // 限制单次生成的粒子数量以避免性能问题
            particlesToSpawnNow = Math.min(particlesToSpawnNow, 10);
            
            // 生成粒子
            for (int i = 0; i < particlesToSpawnNow; i++) {
                if (activeParticles.size() >= maxParticles) {
                    break;
                }
                
                spawnSingleParticle();
            }
            
        } catch (Exception e) {
            StormWeaver.LOGGER.error("根据稳定速率生成粒子时发生错误", e);
        }
    }
    
    /**
     * 生成单个粒子
     */
    private void spawnSingleParticle() {
        try {
            // 计算粒子初始位置和速度
            Vec3 particlePos = calculateParticlePosition();
            Vec3 particleVel = calculateParticleVelocity(particlePos);
            
            // 使用Minecraft的粒子系统生成粒子
            // 这会触发我们的ParticleProvider.createParticle方法
            level.addParticle(
                StormWeaver.BLIZZARD_PARTICLE.get(),
                particlePos.x, particlePos.y, particlePos.z,
                particleVel.x, particleVel.y, particleVel.z
            );
            
            if (Config.enableDebugLogging) {
                StormWeaver.LOGGER.debug("生成粒子在位置: {}, 速度: {}", particlePos, particleVel);
            }
            
        } catch (Exception e) {
            StormWeaver.LOGGER.error("生成单个粒子时发生错误", e);
        }
    }
    
    /**
     * 计算粒子初始位置
     * 
     * @return 粒子位置
     */
    private Vec3 calculateParticlePosition() {
        Vec3 basePos = position;
        
        // 处理形状组件
        if (hasShapeSphere && shapeSphere != null) {
            return calculateSpherePosition(basePos);
        }
        
        // 默认从发射器位置生成
        return basePos;
    }
    
    /**
     * 计算球体形状的粒子位置
     * 
     * @param basePos 基础位置
     * @return 粒子位置
     */
    private Vec3 calculateSpherePosition(Vec3 basePos) {
        try {
            // 获取偏移量
            String[] offsetExpr = shapeSphere.getOffset();
            Vec3 offset = Vec3.ZERO;
            if (offsetExpr != null && offsetExpr.length >= 3) {
                double ox = moLangEvaluator.evaluateExpression(offsetExpr[0]);
                double oy = moLangEvaluator.evaluateExpression(offsetExpr[1]);
                double oz = moLangEvaluator.evaluateExpression(offsetExpr[2]);
                offset = new Vec3(ox, oy, oz);
            }
            
            // 获取半径
            String radiusExpr = shapeSphere.getRadius();
            double radius = moLangEvaluator.evaluateExpression(radiusExpr);
            
            // 生成随机位置
            Vec3 randomPos;
            if (shapeSphere.isSurfaceOnly()) {
                // 只在球面上生成
                randomPos = generateRandomSpherePoint(radius);
            } else {
                // 在球体内部生成
                double r = radius * Math.cbrt(random.nextDouble()); // 立方根分布确保均匀分布
                randomPos = generateRandomSpherePoint(r);
            }
            
            return basePos.add(offset).add(randomPos);
            
        } catch (Exception e) {
            StormWeaver.LOGGER.error("计算球体位置时发生错误", e);
            return basePos;
        }
    }
    
    /**
     * 生成球面上的随机点
     * 
     * @param radius 半径
     * @return 随机位置向量
     */
    private Vec3 generateRandomSpherePoint(double radius) {
        // 使用球坐标系生成均匀分布的点
        double theta = random.nextDouble() * 2 * Math.PI; // 方位角
        double phi = Math.acos(2 * random.nextDouble() - 1); // 极角
        
        double x = radius * Math.sin(phi) * Math.cos(theta);
        double y = radius * Math.sin(phi) * Math.sin(theta);
        double z = radius * Math.cos(phi);
        
        return new Vec3(x, y, z);
    }
    
    /**
     * 计算粒子初始速度
     * 
     * @param particlePos 粒子位置
     * @return 粒子速度
     */
    private Vec3 calculateParticleVelocity(Vec3 particlePos) {
        // 处理形状组件的方向设置
        if (hasShapeSphere && shapeSphere != null) {
            return calculateSphereVelocity(particlePos);
        }
        
        // 默认无初始速度
        return Vec3.ZERO;
    }
    
    /**
     * 计算球体形状的粒子速度
     * 
     * @param particlePos 粒子位置
     * @return 粒子速度
     */
    private Vec3 calculateSphereVelocity(Vec3 particlePos) {
        try {
            Object direction = shapeSphere.getDirection();
            
            if (direction instanceof String) {
                String dirStr = (String) direction;
                if ("outwards".equals(dirStr)) {
                    // 向外方向
                    return particlePos.subtract(position).normalize().scale(0.1);
                } else if ("inwards".equals(dirStr)) {
                    // 向内方向
                    return position.subtract(particlePos).normalize().scale(0.1);
                }
            } else if (direction instanceof String[]) {
                // 自定义方向
                String[] dirArray = (String[]) direction;
                if (dirArray.length >= 3) {
                    double vx = moLangEvaluator.evaluateExpression(dirArray[0]);
                    double vy = moLangEvaluator.evaluateExpression(dirArray[1]);
                    double vz = moLangEvaluator.evaluateExpression(dirArray[2]);
                    return new Vec3(vx, vy, vz);
                }
            }
            
        } catch (Exception e) {
            StormWeaver.LOGGER.error("计算球体速度时发生错误", e);
        }
        
        return Vec3.ZERO;
    }
    
    /**
     * 使发射器过期
     */
    public void expire() {
        this.shouldRemove = true;
        this.isActive = false;
        
        if (Config.enableDebugLogging) {
            StormWeaver.LOGGER.debug("粒子发射器过期: {}", effectData.getIdentifier());
        }
    }
    
    /**
     * 检查发射器是否应该被移除
     * 
     * @return 如果应该移除返回true
     */
    public boolean shouldRemove() {
        return shouldRemove;
    }
    
    /**
     * 获取活跃粒子数量
     * 
     * @return 粒子数量
     */
    public int getParticleCount() {
        return activeParticles.size();
    }
    
    /**
     * 获取发射器年龄
     * 
     * @return 年龄（ticks）
     */
    public int getAge() {
        return age;
    }
    
    /**
     * 获取发射器位置
     * 
     * @return 位置
     */
    public Vec3 getPosition() {
        return position;
    }
    
    /**
     * 获取粒子效果数据
     * 
     * @return 粒子效果数据
     */
    public BlizzardParticleEffect getEffectData() {
        return effectData;
    }
}
