package com.stormweaver.stormweaver.particle;

import com.stormweaver.stormweaver.Config;
import com.stormweaver.stormweaver.StormWeaver;
import com.stormweaver.stormweaver.data.BlizzardParticleEffect;
import com.stormweaver.stormweaver.network.ClientPacketHandler;
import net.minecraft.client.multiplayer.ClientLevel;
import net.minecraft.client.particle.Particle;
import net.minecraft.client.particle.ParticleProvider;
import net.minecraft.client.particle.SpriteSet;
import net.minecraft.core.particles.SimpleParticleType;
import net.minecraft.resources.ResourceLocation;
import net.minecraftforge.api.distmarker.Dist;
import net.minecraftforge.api.distmarker.OnlyIn;

/**
 * 暴雪粒子提供者
 * 
 * 这个类是整个系统的关键"注入点"，负责创建BlizzardParticle实例
 * 
 * 挑战：标准的createParticle方法签名中没有参数可以让我们直接传入
 * 解析好的BlizzardParticleEffect数据对象
 * 
 * 解决方案：通过ClientPacketHandler的ThreadLocal机制获取上下文信息
 * 
 * 工作流程：
 * 1. 服务器发送自定义网络数据包，包含粒子效果ID和坐标
 * 2. 客户端网络处理器接收数据包，将ResourceLocation存放在ThreadLocal中
 * 3. 调用原版的ClientLevel.addParticle方法
 * 4. 游戏引擎调用这个ParticleProvider的createParticle方法
 * 5. 在createParticle中从ThreadLocal读取ResourceLocation
 * 6. 使用ResourceLocation从缓存管理器获取BlizzardParticleEffect对象
 * 7. 将数据对象传递给BlizzardParticle构造函数
 * 
 * <AUTHOR>
 */
@OnlyIn(Dist.CLIENT)
public class BlizzardParticleProvider implements ParticleProvider<SimpleParticleType> {
    
    // SpriteSet对象，虽然我们使用自定义渲染，但仍需接收它以满足接口要求
    private final SpriteSet spriteSet;
    
    /**
     * 构造函数
     * 
     * @param spriteSet 精灵集合，用于标准的粒子纹理渲染
     */
    public BlizzardParticleProvider(SpriteSet spriteSet) {
        this.spriteSet = spriteSet;
        
        if (Config.enableDebugLogging) {
            StormWeaver.LOGGER.debug("创建暴雪粒子提供者");
        }
    }
    
    /**
     * 创建粒子实例
     * 
     * 这是ParticleProvider接口的核心方法，负责创建具体的粒子实例
     * 
     * @param type 粒子类型（我们的通用BlizzardParticleType）
     * @param level 客户端世界
     * @param x X坐标
     * @param y Y坐标
     * @param z Z坐标
     * @param xd X方向速度
     * @param yd Y方向速度
     * @param zd Z方向速度
     * @return 创建的粒子实例，如果创建失败则返回null
     */
    @Override
    public Particle createParticle(SimpleParticleType type, ClientLevel level, 
                                 double x, double y, double z, 
                                 double xd, double yd, double zd) {
        
        try {
            // 1. 从线程局部变量中获取效果ID
            ResourceLocation effectId = ClientPacketHandler.getCurrentEffectId();
            if (effectId == null) {
                if (Config.enableDebugLogging) {
                    StormWeaver.LOGGER.debug("没有找到当前效果ID，无法创建粒子");
                }
                return null;
            }
            
            // 2. 从管理器中获取预解析的数据
            BlizzardParticleEffect effectData = BlizzardParticleManager.getInstance().getEffect(effectId);
            if (effectData == null) {
                StormWeaver.LOGGER.warn("未找到粒子效果数据: {}", effectId);
                return null;
            }
            
            // 3. 验证效果数据的有效性
            if (!effectData.isValid()) {
                StormWeaver.LOGGER.warn("粒子效果数据无效: {}", effectId);
                return null;
            }
            
            // 4. 实例化自定义粒子，并注入数据
            BlizzardParticle particle = new BlizzardParticle(
                level, x, y, z, xd, yd, zd, 
                effectData, spriteSet
            );
            
            if (Config.enableDebugLogging) {
                StormWeaver.LOGGER.debug("成功创建暴雪粒子: {} 在位置 ({}, {}, {})", 
                        effectId, x, y, z);
            }
            
            return particle;
            
        } catch (Exception e) {
            StormWeaver.LOGGER.error("创建暴雪粒子时发生错误", e);
            return null;
        }
    }
    
    /**
     * 获取精灵集合
     * 
     * @return 精灵集合
     */
    public SpriteSet getSpriteSet() {
        return spriteSet;
    }
}
