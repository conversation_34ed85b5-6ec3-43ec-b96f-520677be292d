package com.stormweaver.stormweaver.particle;

import com.mojang.serialization.Codec;
import net.minecraft.core.particles.ParticleOptions;
import net.minecraft.core.particles.ParticleType;
import net.minecraft.network.FriendlyByteBuf;

/**
 * 暴雪粒子类型
 * 
 * 这是一个通用的粒子类型，用于处理所有基岩版粒子效果
 * 与传统的Forge粒子开发模式不同，我们不为每个JSON文件创建独立的ParticleType
 * 而是使用单一的通用ParticleType，具体的粒子行为由加载的JSON文件动态决定
 * 
 * 这种设计的优势：
 * 1. 简化注册流程
 * 2. 支持数据驱动的粒子系统
 * 3. 高度的灵活性和可扩展性
 * 4. 无需重新编译即可添加新的粒子效果
 * 
 * <AUTHOR>
 */
public class BlizzardParticleType extends ParticleType<BlizzardParticleOptions> {
    
    /**
     * 构造函数
     * 
     * @param overrideLimiter 是否覆盖粒子限制器
     */
    public BlizzardParticleType(boolean overrideLimiter) {
        super(overrideLimiter, BlizzardParticleOptions.DESERIALIZER);
    }
    
    /**
     * 获取编解码器
     * 
     * @return 粒子选项的编解码器
     */
    @Override
    public Codec<BlizzardParticleOptions> codec() {
        return BlizzardParticleOptions.CODEC;
    }
    
    /**
     * 暴雪粒子选项
     * 
     * 这个类定义了粒子的参数，虽然我们主要通过JSON文件控制粒子行为，
     * 但仍然需要这个类来满足Forge的要求
     */
    public static class BlizzardParticleOptions implements ParticleOptions {
        
        // 编解码器，用于序列化和反序列化
        public static final Codec<BlizzardParticleOptions> CODEC = Codec.unit(new BlizzardParticleOptions());
        
        // 反序列化器，用于从网络缓冲区读取数据
        public static final ParticleOptions.Deserializer<BlizzardParticleOptions> DESERIALIZER = 
            new ParticleOptions.Deserializer<BlizzardParticleOptions>() {
                @Override
                public BlizzardParticleOptions fromCommand(ParticleType<BlizzardParticleOptions> particleType, 
                                                          com.mojang.brigadier.StringReader reader) {
                    // 从命令行解析参数
                    // 对于我们的用例，这里可以保持简单
                    return new BlizzardParticleOptions();
                }
                
                @Override
                public BlizzardParticleOptions fromNetwork(ParticleType<BlizzardParticleOptions> particleType, 
                                                          FriendlyByteBuf buffer) {
                    // 从网络缓冲区读取参数
                    // 我们的粒子数据主要通过自定义网络包传输，这里保持简单
                    return new BlizzardParticleOptions();
                }
            };
        
        /**
         * 获取粒子类型
         * 
         * @return 粒子类型
         */
        @Override
        public ParticleType<?> getType() {
            return com.stormweaver.stormweaver.StormWeaver.BLIZZARD_PARTICLE.get();
        }
        
        /**
         * 写入到网络缓冲区
         * 
         * @param buffer 网络缓冲区
         */
        @Override
        public void writeToNetwork(FriendlyByteBuf buffer) {
            // 我们的粒子数据主要通过自定义网络包传输
            // 这里不需要写入额外数据
        }
        
        /**
         * 写入到字符串
         * 
         * @return 字符串表示
         */
        @Override
        public String writeToString() {
            return "blizzard_particle";
        }
    }
}
