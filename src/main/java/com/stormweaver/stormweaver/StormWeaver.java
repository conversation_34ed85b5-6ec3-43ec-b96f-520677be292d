package com.stormweaver.stormweaver;

import com.mojang.logging.LogUtils;
import com.stormweaver.stormweaver.particle.BlizzardParticleManager;
import com.stormweaver.stormweaver.particle.BlizzardParticleProvider;
import com.stormweaver.stormweaver.network.NetworkHandler;
import com.stormweaver.stormweaver.commands.ParticleTestCommand;
import net.minecraft.core.particles.ParticleType;
import net.minecraft.core.particles.SimpleParticleType;
import net.minecraftforge.api.distmarker.Dist;
import net.minecraftforge.client.event.RegisterParticleProvidersEvent;
import net.minecraftforge.common.MinecraftForge;
import net.minecraftforge.event.server.ServerStartingEvent;
import net.minecraftforge.eventbus.api.IEventBus;
import net.minecraftforge.eventbus.api.SubscribeEvent;
import net.minecraftforge.fml.ModLoadingContext;
import net.minecraftforge.fml.common.Mod;
import net.minecraftforge.fml.config.ModConfig;
import net.minecraftforge.fml.event.lifecycle.FMLClientSetupEvent;
import net.minecraftforge.fml.event.lifecycle.FMLCommonSetupEvent;
import net.minecraftforge.fml.javafmlmod.FMLJavaModLoadingContext;
import net.minecraftforge.registries.DeferredRegister;
import net.minecraftforge.registries.ForgeRegistries;
import net.minecraftforge.registries.RegistryObject;
import net.minecraftforge.event.RegisterCommandsEvent;
import org.slf4j.Logger;

/**
 * StormWeaver主模组类
 * 
 * 这个模组的核心功能是将Minecraft基岩版的粒子效果JSON配置文件
 * 转换为Minecraft Java版Forge的粒子API调用。
 * 
 * 主要特性：
 * - 解析基岩版粒子JSON格式
 * - 实现MoLang表达式求值引擎
 * - 提供完整的数据驱动粒子系统
 * - 支持动态纹理和复杂粒子行为
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Mod(StormWeaver.MODID)
public class StormWeaver {
    
    // 模组ID，用于标识这个模组
    public static final String MODID = "stormweaver";
    
    // 日志记录器，用于输出调试和错误信息
    private static final Logger LOGGER = LogUtils.getLogger();
    
    // 粒子类型注册器
    public static final DeferredRegister<ParticleType<?>> PARTICLE_TYPES = 
        DeferredRegister.create(ForgeRegistries.PARTICLE_TYPES, MODID);
    
    // 注册通用的暴雪粒子类型
    // 我们使用单一的通用ParticleType来处理所有基岩版粒子效果
    // 具体的粒子行为由JSON文件动态决定
    public static final RegistryObject<SimpleParticleType> BLIZZARD_PARTICLE = 
        PARTICLE_TYPES.register("blizzard_particle", () -> new SimpleParticleType(false));
    
    public StormWeaver() {
        IEventBus modEventBus = FMLJavaModLoadingContext.get().getModEventBus();
        
        // 注册模组设置事件监听器
        modEventBus.addListener(this::commonSetup);
        modEventBus.addListener(this::clientSetup);
        
        // 注册粒子类型
        PARTICLE_TYPES.register(modEventBus);
        
        // 注册这个模组到Forge事件总线
        MinecraftForge.EVENT_BUS.register(this);
        
        // 注册模组配置
        ModLoadingContext.get().registerConfig(ModConfig.Type.COMMON, Config.SPEC);
        
        LOGGER.info("StormWeaver模组正在初始化...");
        LOGGER.info("准备加载基岩版粒子系统转换器");
    }
    
    /**
     * 通用设置阶段
     * 在这里初始化服务器端和客户端都需要的组件
     */
    private void commonSetup(final FMLCommonSetupEvent event) {
        LOGGER.info("StormWeaver通用设置阶段开始");
        
        // 初始化网络处理器
        NetworkHandler.register();
        
        LOGGER.info("网络处理器已注册");
        LOGGER.info("StormWeaver通用设置完成");
    }
    
    /**
     * 客户端设置阶段
     * 在这里初始化只有客户端需要的组件
     */
    private void clientSetup(final FMLClientSetupEvent event) {
        LOGGER.info("StormWeaver客户端设置阶段开始");
        
        // 初始化粒子效果管理器
        BlizzardParticleManager.getInstance().initialize();
        
        LOGGER.info("暴雪粒子管理器已初始化");
        LOGGER.info("StormWeaver客户端设置完成");
    }
    
    /**
     * 服务器启动事件处理
     */
    @SubscribeEvent
    public void onServerStarting(ServerStartingEvent event) {
        LOGGER.info("StormWeaver: 服务器正在启动");
        // 这里可以添加服务器启动时需要执行的逻辑
    }

    /**
     * 注册命令事件处理
     */
    @SubscribeEvent
    public void onRegisterCommands(RegisterCommandsEvent event) {
        LOGGER.info("注册StormWeaver命令");
        ParticleTestCommand.register(event.getDispatcher());
    }
    
    /**
     * 客户端粒子提供者注册事件
     * 这个事件只在客户端触发
     */
    @Mod.EventBusSubscriber(modid = MODID, bus = Mod.EventBusSubscriber.Bus.MOD, value = Dist.CLIENT)
    public static class ClientModEvents {
        
        @SubscribeEvent
        public static void onParticleFactoryRegistration(RegisterParticleProvidersEvent event) {
            LOGGER.info("注册暴雪粒子提供者");
            
            // 注册我们的自定义粒子提供者
            // 这个提供者负责创建BlizzardParticle实例
            event.registerSpriteSet(BLIZZARD_PARTICLE.get(), BlizzardParticleProvider::new);
            
            LOGGER.info("暴雪粒子提供者注册完成");
        }
    }
}
