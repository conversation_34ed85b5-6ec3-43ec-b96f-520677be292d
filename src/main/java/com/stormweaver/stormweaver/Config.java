package com.stormweaver.stormweaver;

import net.minecraftforge.common.ForgeConfigSpec;
import net.minecraftforge.eventbus.api.SubscribeEvent;
import net.minecraftforge.fml.common.Mod;
import net.minecraftforge.fml.event.config.ModConfigEvent;

/**
 * StormWeaver模组配置类
 * 
 * 这个类管理模组的所有配置选项，包括：
 * - 粒子效果性能设置
 * - 调试选项
 * - 资源加载设置
 * 
 * <AUTHOR>
 */
@Mod.EventBusSubscriber(modid = StormWeaver.MODID, bus = Mod.EventBusSubscriber.Bus.MOD)
public class Config {
    
    private static final ForgeConfigSpec.Builder BUILDER = new ForgeConfigSpec.Builder();
    
    // 性能相关配置
    public static final ForgeConfigSpec.IntValue MAX_PARTICLES_PER_EMITTER;
    public static final ForgeConfigSpec.IntValue MAX_ACTIVE_EMITTERS;
    public static final ForgeConfigSpec.DoubleValue PARTICLE_RENDER_DISTANCE;
    
    // 调试相关配置
    public static final ForgeConfigSpec.BooleanValue ENABLE_DEBUG_LOGGING;
    public static final ForgeConfigSpec.BooleanValue SHOW_PARTICLE_DEBUG_INFO;
    public static final ForgeConfigSpec.BooleanValue ENABLE_PERFORMANCE_MONITORING;
    
    // 资源加载相关配置
    public static final ForgeConfigSpec.BooleanValue ENABLE_RESOURCE_CACHING;
    public static final ForgeConfigSpec.IntValue CACHE_SIZE_LIMIT;
    public static final ForgeConfigSpec.BooleanValue PRELOAD_ALL_EFFECTS;
    
    // MoLang引擎相关配置
    public static final ForgeConfigSpec.BooleanValue ENABLE_MOLANG_OPTIMIZATION;
    public static final ForgeConfigSpec.IntValue EXPRESSION_CACHE_SIZE;
    
    static {
        BUILDER.comment("StormWeaver粒子系统配置")
               .comment("这些设置控制粒子效果的性能和行为")
               .push("performance");
        
        MAX_PARTICLES_PER_EMITTER = BUILDER
            .comment("每个发射器允许的最大粒子数量")
            .comment("降低此值可以提高性能，但可能影响视觉效果")
            .defineInRange("maxParticlesPerEmitter", 1000, 1, 10000);
        
        MAX_ACTIVE_EMITTERS = BUILDER
            .comment("同时活跃的发射器最大数量")
            .comment("超过此数量时，旧的发射器将被移除")
            .defineInRange("maxActiveEmitters", 100, 1, 1000);
        
        PARTICLE_RENDER_DISTANCE = BUILDER
            .comment("粒子渲染距离（方块）")
            .comment("超过此距离的粒子将不会被渲染")
            .defineInRange("particleRenderDistance", 64.0, 8.0, 256.0);
        
        BUILDER.pop();
        
        BUILDER.comment("调试和开发相关设置")
               .push("debug");
        
        ENABLE_DEBUG_LOGGING = BUILDER
            .comment("启用详细的调试日志")
            .comment("警告：这会产生大量日志输出")
            .define("enableDebugLogging", false);
        
        SHOW_PARTICLE_DEBUG_INFO = BUILDER
            .comment("在游戏中显示粒子调试信息")
            .comment("显示粒子数量、发射器状态等信息")
            .define("showParticleDebugInfo", false);
        
        ENABLE_PERFORMANCE_MONITORING = BUILDER
            .comment("启用性能监控")
            .comment("监控粒子系统的性能指标")
            .define("enablePerformanceMonitoring", false);
        
        BUILDER.pop();
        
        BUILDER.comment("资源加载和缓存设置")
               .push("resources");
        
        ENABLE_RESOURCE_CACHING = BUILDER
            .comment("启用资源缓存")
            .comment("缓存已解析的粒子效果以提高性能")
            .define("enableResourceCaching", true);
        
        CACHE_SIZE_LIMIT = BUILDER
            .comment("缓存大小限制（MB）")
            .comment("超过此大小时将清理最少使用的缓存")
            .defineInRange("cacheSizeLimit", 64, 16, 512);
        
        PRELOAD_ALL_EFFECTS = BUILDER
            .comment("预加载所有粒子效果")
            .comment("在游戏启动时加载所有效果，可能增加启动时间但提高运行时性能")
            .define("preloadAllEffects", false);
        
        BUILDER.pop();
        
        BUILDER.comment("MoLang表达式引擎设置")
               .push("molang");
        
        ENABLE_MOLANG_OPTIMIZATION = BUILDER
            .comment("启用MoLang表达式优化")
            .comment("缓存编译后的表达式以提高求值性能")
            .define("enableMolangOptimization", true);
        
        EXPRESSION_CACHE_SIZE = BUILDER
            .comment("表达式缓存大小")
            .comment("缓存的编译表达式数量")
            .defineInRange("expressionCacheSize", 1000, 100, 10000);
        
        BUILDER.pop();
    }
    
    public static final ForgeConfigSpec SPEC = BUILDER.build();
    
    // 运行时配置值
    public static int maxParticlesPerEmitter;
    public static int maxActiveEmitters;
    public static double particleRenderDistance;
    public static boolean enableDebugLogging;
    public static boolean showParticleDebugInfo;
    public static boolean enablePerformanceMonitoring;
    public static boolean enableResourceCaching;
    public static int cacheSizeLimit;
    public static boolean preloadAllEffects;
    public static boolean enableMolangOptimization;
    public static int expressionCacheSize;
    
    @SubscribeEvent
    static void onLoad(final ModConfigEvent event) {
        // 当配置加载或重新加载时更新运行时值
        maxParticlesPerEmitter = MAX_PARTICLES_PER_EMITTER.get();
        maxActiveEmitters = MAX_ACTIVE_EMITTERS.get();
        particleRenderDistance = PARTICLE_RENDER_DISTANCE.get();
        enableDebugLogging = ENABLE_DEBUG_LOGGING.get();
        showParticleDebugInfo = SHOW_PARTICLE_DEBUG_INFO.get();
        enablePerformanceMonitoring = ENABLE_PERFORMANCE_MONITORING.get();
        enableResourceCaching = ENABLE_RESOURCE_CACHING.get();
        cacheSizeLimit = CACHE_SIZE_LIMIT.get();
        preloadAllEffects = PRELOAD_ALL_EFFECTS.get();
        enableMolangOptimization = ENABLE_MOLANG_OPTIMIZATION.get();
        expressionCacheSize = EXPRESSION_CACHE_SIZE.get();
        
        StormWeaver.LOGGER.info("StormWeaver配置已加载");
        if (enableDebugLogging) {
            StormWeaver.LOGGER.info("调试模式已启用");
        }
    }
}
