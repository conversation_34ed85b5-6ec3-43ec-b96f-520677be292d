package com.stormweaver.stormweaver.data;

import com.google.gson.annotations.SerializedName;
import com.stormweaver.stormweaver.data.components.*;
import com.stormweaver.stormweaver.data.curves.ParticleCurves;
import com.stormweaver.stormweaver.data.events.ParticleEvents;

/**
 * 基岩版粒子效果的主数据模型
 * 
 * 这个类对应基岩版粒子JSON文件的根结构，包含：
 * - 格式版本信息
 * - 粒子效果描述
 * - 曲线定义
 * - 事件定义
 * - 组件集合
 * 
 * JSON结构示例：
 * {
 *   "format_version": "1.10.0",
 *   "particle_effect": {
 *     "description": { ... },
 *     "curves": { ... },
 *     "events": { ... },
 *     "components": { ... }
 *   }
 * }
 * 
 * <AUTHOR>
 */
public class BlizzardParticleEffect {
    
    /**
     * 格式版本，用于确保兼容性
     */
    @SerializedName("format_version")
    private String formatVersion;
    
    /**
     * 粒子效果的主要内容
     */
    @SerializedName("particle_effect")
    private ParticleEffectData particleEffect;
    
    // Getters and Setters
    public String getFormatVersion() {
        return formatVersion;
    }
    
    public void setFormatVersion(String formatVersion) {
        this.formatVersion = formatVersion;
    }
    
    public ParticleEffectData getParticleEffect() {
        return particleEffect;
    }
    
    public void setParticleEffect(ParticleEffectData particleEffect) {
        this.particleEffect = particleEffect;
    }
    
    /**
     * 粒子效果数据的内部类
     */
    public static class ParticleEffectData {
        
        /**
         * 粒子效果的描述信息
         */
        private Description description;
        
        /**
         * 曲线定义，用于复杂的数值变化
         */
        private ParticleCurves curves;
        
        /**
         * 事件定义，用于粒子生命周期中的事件处理
         */
        private ParticleEvents events;
        
        /**
         * 组件集合，定义粒子的具体行为
         */
        private ParticleComponents components;
        
        // Getters and Setters
        public Description getDescription() {
            return description;
        }
        
        public void setDescription(Description description) {
            this.description = description;
        }
        
        public ParticleCurves getCurves() {
            return curves;
        }
        
        public void setCurves(ParticleCurves curves) {
            this.curves = curves;
        }
        
        public ParticleEvents getEvents() {
            return events;
        }
        
        public void setEvents(ParticleEvents events) {
            this.events = events;
        }
        
        public ParticleComponents getComponents() {
            return components;
        }
        
        public void setComponents(ParticleComponents components) {
            this.components = components;
        }
    }
    
    /**
     * 粒子效果描述信息
     */
    public static class Description {
        
        /**
         * 粒子效果的唯一标识符
         * 例如: "minecraft:test_effect"
         */
        private String identifier;
        
        /**
         * 基本渲染参数
         */
        @SerializedName("basic_render_parameters")
        private BasicRenderParameters basicRenderParameters;
        
        // Getters and Setters
        public String getIdentifier() {
            return identifier;
        }
        
        public void setIdentifier(String identifier) {
            this.identifier = identifier;
        }
        
        public BasicRenderParameters getBasicRenderParameters() {
            return basicRenderParameters;
        }
        
        public void setBasicRenderParameters(BasicRenderParameters basicRenderParameters) {
            this.basicRenderParameters = basicRenderParameters;
        }
    }
    
    /**
     * 基本渲染参数
     */
    public static class BasicRenderParameters {
        
        /**
         * 材质类型
         * 例如: "particles_alpha", "particles_blend", "particles_add"
         */
        private String material;
        
        /**
         * 纹理路径
         * 例如: "textures/particle/particles"
         */
        private String texture;
        
        // Getters and Setters
        public String getMaterial() {
            return material;
        }
        
        public void setMaterial(String material) {
            this.material = material;
        }
        
        public String getTexture() {
            return texture;
        }
        
        public void setTexture(String texture) {
            this.texture = texture;
        }
    }
    
    /**
     * 验证粒子效果数据的完整性
     * 
     * @return 如果数据有效返回true，否则返回false
     */
    public boolean isValid() {
        if (formatVersion == null || formatVersion.isEmpty()) {
            return false;
        }
        
        if (particleEffect == null) {
            return false;
        }
        
        if (particleEffect.description == null) {
            return false;
        }
        
        if (particleEffect.description.identifier == null || 
            particleEffect.description.identifier.isEmpty()) {
            return false;
        }
        
        if (particleEffect.description.basicRenderParameters == null) {
            return false;
        }
        
        if (particleEffect.description.basicRenderParameters.material == null ||
            particleEffect.description.basicRenderParameters.texture == null) {
            return false;
        }
        
        return true;
    }
    
    /**
     * 获取粒子效果的标识符
     * 
     * @return 粒子效果标识符，如果无效则返回null
     */
    public String getIdentifier() {
        if (particleEffect != null && particleEffect.description != null) {
            return particleEffect.description.identifier;
        }
        return null;
    }
    
    /**
     * 获取材质类型
     * 
     * @return 材质类型，如果无效则返回null
     */
    public String getMaterial() {
        if (particleEffect != null && 
            particleEffect.description != null && 
            particleEffect.description.basicRenderParameters != null) {
            return particleEffect.description.basicRenderParameters.material;
        }
        return null;
    }
    
    /**
     * 获取纹理路径
     * 
     * @return 纹理路径，如果无效则返回null
     */
    public String getTexture() {
        if (particleEffect != null && 
            particleEffect.description != null && 
            particleEffect.description.basicRenderParameters != null) {
            return particleEffect.description.basicRenderParameters.texture;
        }
        return null;
    }
    
    @Override
    public String toString() {
        return "BlizzardParticleEffect{" +
                "formatVersion='" + formatVersion + '\'' +
                ", identifier='" + getIdentifier() + '\'' +
                ", material='" + getMaterial() + '\'' +
                ", texture='" + getTexture() + '\'' +
                '}';
    }
}
