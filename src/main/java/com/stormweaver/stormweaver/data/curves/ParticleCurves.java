package com.stormweaver.stormweaver.data.curves;

/**
 * 粒子曲线定义
 * 
 * 对应基岩版粒子JSON中的curves部分
 * 曲线是插值值，输入从0到1，输出基于曲线
 * 曲线的结果是同名的MoLang变量，可以在组件的MoLang中引用
 * 
 * 这是一个占位符类，将来会实现完整的曲线功能
 * 
 * <AUTHOR>
 */
public class ParticleCurves {
    
    // TODO: 实现曲线功能
    // 支持的曲线类型：
    // - linear: 线性插值
    // - bezier: 贝塞尔曲线
    // - bezier_chain: 贝塞尔链
    // - catmull_rom: Catmull-Rom样条
    
    /**
     * 默认构造函数
     */
    public ParticleCurves() {
    }
}
