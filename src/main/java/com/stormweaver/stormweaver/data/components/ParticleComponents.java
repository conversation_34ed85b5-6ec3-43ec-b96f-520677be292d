package com.stormweaver.stormweaver.data.components;

import com.google.gson.annotations.SerializedName;
import com.stormweaver.stormweaver.data.components.emitter.*;
import com.stormweaver.stormweaver.data.components.particle.*;

/**
 * 粒子组件集合
 * 
 * 这个类包含了所有可能的粒子组件，分为两大类：
 * 1. 发射器组件 (Emitter Components) - 控制粒子的生成
 * 2. 粒子组件 (Particle Components) - 控制单个粒子的行为
 * 
 * 基岩版粒子系统采用组件化设计，通过组合不同的组件来实现复杂的粒子效果。
 * 
 * <AUTHOR>
 */
public class ParticleComponents {
    
    // ========== 发射器组件 ==========
    
    /**
     * 发射器生命周期 - 一次性
     * 发射器执行一次后过期
     */
    @SerializedName("minecraft:emitter_lifetime_once")
    private EmitterLifetimeOnce emitterLifetimeOnce;
    
    /**
     * 发射器生命周期 - 循环
     * 发射器会循环执行直到被移除
     */
    @SerializedName("minecraft:emitter_lifetime_looping")
    private EmitterLifetimeLooping emitterLifetimeLooping;
    
    /**
     * 发射器生命周期 - 表达式控制
     * 通过MoLang表达式控制发射器的激活和过期
     */
    @SerializedName("minecraft:emitter_lifetime_expression")
    private EmitterLifetimeExpression emitterLifetimeExpression;
    
    /**
     * 发射器生命周期事件
     * 在发射器生命周期的特定时刻触发事件
     */
    @SerializedName("minecraft:emitter_lifetime_events")
    private EmitterLifetimeEvents emitterLifetimeEvents;
    
    /**
     * 发射器速率 - 瞬间
     * 所有粒子同时发射
     */
    @SerializedName("minecraft:emitter_rate_instant")
    private EmitterRateInstant emitterRateInstant;
    
    /**
     * 发射器速率 - 稳定
     * 粒子以稳定的速率发射
     */
    @SerializedName("minecraft:emitter_rate_steady")
    private EmitterRateSteady emitterRateSteady;
    
    /**
     * 发射器速率 - 手动
     * 粒子发射由游戏逻辑手动控制
     */
    @SerializedName("minecraft:emitter_rate_manual")
    private EmitterRateManual emitterRateManual;
    
    /**
     * 发射器形状 - 点
     * 粒子从一个点发射
     */
    @SerializedName("minecraft:emitter_shape_point")
    private EmitterShapePoint emitterShapePoint;
    
    /**
     * 发射器形状 - 球体
     * 粒子从球体表面或内部发射
     */
    @SerializedName("minecraft:emitter_shape_sphere")
    private EmitterShapeSphere emitterShapeSphere;
    
    /**
     * 发射器形状 - 盒子
     * 粒子从盒子表面或内部发射
     */
    @SerializedName("minecraft:emitter_shape_box")
    private EmitterShapeBox emitterShapeBox;
    
    /**
     * 发射器形状 - 圆盘
     * 粒子从圆盘表面或内部发射
     */
    @SerializedName("minecraft:emitter_shape_disc")
    private EmitterShapeDisc emitterShapeDisc;
    
    /**
     * 发射器形状 - 自定义
     * 通过MoLang表达式自定义发射位置和方向
     */
    @SerializedName("minecraft:emitter_shape_custom")
    private EmitterShapeCustom emitterShapeCustom;
    
    /**
     * 发射器形状 - 实体AABB
     * 粒子从实体的轴对齐包围盒发射
     */
    @SerializedName("minecraft:emitter_shape_entity_aabb")
    private EmitterShapeEntityAABB emitterShapeEntityAABB;
    
    /**
     * 发射器本地空间
     * 控制粒子是在实体空间还是世界空间中模拟
     */
    @SerializedName("minecraft:emitter_local_space")
    private EmitterLocalSpace emitterLocalSpace;
    
    /**
     * 发射器初始化
     * 在发射器创建时运行MoLang表达式
     */
    @SerializedName("minecraft:emitter_initialization")
    private EmitterInitialization emitterInitialization;
    
    // ========== 粒子组件 ==========
    
    /**
     * 粒子初始速度
     * 设置粒子的初始速度
     */
    @SerializedName("minecraft:particle_initial_speed")
    private ParticleInitialSpeed particleInitialSpeed;
    
    /**
     * 粒子初始旋转
     * 设置粒子的初始旋转和旋转速率
     */
    @SerializedName("minecraft:particle_initial_spin")
    private ParticleInitialSpin particleInitialSpin;
    
    /**
     * 粒子生命周期表达式
     * 控制粒子的生命周期
     */
    @SerializedName("minecraft:particle_lifetime_expression")
    private ParticleLifetimeExpression particleLifetimeExpression;
    
    /**
     * 粒子生命周期事件
     * 在粒子生命周期的特定时刻触发事件
     */
    @SerializedName("minecraft:particle_lifetime_events")
    private ParticleLifetimeEvents particleLifetimeEvents;
    
    /**
     * 粒子运动 - 动态
     * 通过物理力学控制粒子运动
     */
    @SerializedName("minecraft:particle_motion_dynamic")
    private ParticleMotionDynamic particleMotionDynamic;
    
    /**
     * 粒子运动 - 参数化
     * 通过参数方程直接控制粒子位置
     */
    @SerializedName("minecraft:particle_motion_parametric")
    private ParticleMotionParametric particleMotionParametric;
    
    /**
     * 粒子运动 - 碰撞
     * 处理粒子与地形的碰撞
     */
    @SerializedName("minecraft:particle_motion_collision")
    private ParticleMotionCollision particleMotionCollision;
    
    /**
     * 粒子外观 - 广告牌
     * 控制粒子的渲染外观
     */
    @SerializedName("minecraft:particle_appearance_billboard")
    private ParticleAppearanceBillboard particleAppearanceBillboard;
    
    /**
     * 粒子外观 - 着色
     * 控制粒子的颜色和透明度
     */
    @SerializedName("minecraft:particle_appearance_tinting")
    private ParticleAppearanceTinting particleAppearanceTinting;
    
    /**
     * 粒子外观 - 光照
     * 启用粒子的光照效果
     */
    @SerializedName("minecraft:particle_appearance_lighting")
    private ParticleAppearanceLighting particleAppearanceLighting;
    
    /**
     * 粒子过期条件 - 在指定方块中
     * 当粒子在指定方块中时过期
     */
    @SerializedName("minecraft:particle_expire_if_in_blocks")
    private ParticleExpireIfInBlocks particleExpireIfInBlocks;
    
    /**
     * 粒子过期条件 - 不在指定方块中
     * 当粒子不在指定方块中时过期
     */
    @SerializedName("minecraft:particle_expire_if_not_in_blocks")
    private ParticleExpireIfNotInBlocks particleExpireIfNotInBlocks;
    
    /**
     * 粒子生命周期 - 击杀平面
     * 当粒子穿过指定平面时过期
     */
    @SerializedName("minecraft:particle_kill_plane")
    private ParticleKillPlane particleKillPlane;
    
    // ========== Getters and Setters ==========
    
    // 发射器组件的getter和setter
    public EmitterLifetimeOnce getEmitterLifetimeOnce() { return emitterLifetimeOnce; }
    public void setEmitterLifetimeOnce(EmitterLifetimeOnce emitterLifetimeOnce) { this.emitterLifetimeOnce = emitterLifetimeOnce; }
    
    public EmitterLifetimeLooping getEmitterLifetimeLooping() { return emitterLifetimeLooping; }
    public void setEmitterLifetimeLooping(EmitterLifetimeLooping emitterLifetimeLooping) { this.emitterLifetimeLooping = emitterLifetimeLooping; }
    
    public EmitterLifetimeExpression getEmitterLifetimeExpression() { return emitterLifetimeExpression; }
    public void setEmitterLifetimeExpression(EmitterLifetimeExpression emitterLifetimeExpression) { this.emitterLifetimeExpression = emitterLifetimeExpression; }
    
    public EmitterLifetimeEvents getEmitterLifetimeEvents() { return emitterLifetimeEvents; }
    public void setEmitterLifetimeEvents(EmitterLifetimeEvents emitterLifetimeEvents) { this.emitterLifetimeEvents = emitterLifetimeEvents; }
    
    public EmitterRateInstant getEmitterRateInstant() { return emitterRateInstant; }
    public void setEmitterRateInstant(EmitterRateInstant emitterRateInstant) { this.emitterRateInstant = emitterRateInstant; }
    
    public EmitterRateSteady getEmitterRateSteady() { return emitterRateSteady; }
    public void setEmitterRateSteady(EmitterRateSteady emitterRateSteady) { this.emitterRateSteady = emitterRateSteady; }
    
    public EmitterRateManual getEmitterRateManual() { return emitterRateManual; }
    public void setEmitterRateManual(EmitterRateManual emitterRateManual) { this.emitterRateManual = emitterRateManual; }
    
    public EmitterShapePoint getEmitterShapePoint() { return emitterShapePoint; }
    public void setEmitterShapePoint(EmitterShapePoint emitterShapePoint) { this.emitterShapePoint = emitterShapePoint; }
    
    public EmitterShapeSphere getEmitterShapeSphere() { return emitterShapeSphere; }
    public void setEmitterShapeSphere(EmitterShapeSphere emitterShapeSphere) { this.emitterShapeSphere = emitterShapeSphere; }
    
    public EmitterShapeBox getEmitterShapeBox() { return emitterShapeBox; }
    public void setEmitterShapeBox(EmitterShapeBox emitterShapeBox) { this.emitterShapeBox = emitterShapeBox; }
    
    public EmitterShapeDisc getEmitterShapeDisc() { return emitterShapeDisc; }
    public void setEmitterShapeDisc(EmitterShapeDisc emitterShapeDisc) { this.emitterShapeDisc = emitterShapeDisc; }
    
    public EmitterShapeCustom getEmitterShapeCustom() { return emitterShapeCustom; }
    public void setEmitterShapeCustom(EmitterShapeCustom emitterShapeCustom) { this.emitterShapeCustom = emitterShapeCustom; }
    
    public EmitterShapeEntityAABB getEmitterShapeEntityAABB() { return emitterShapeEntityAABB; }
    public void setEmitterShapeEntityAABB(EmitterShapeEntityAABB emitterShapeEntityAABB) { this.emitterShapeEntityAABB = emitterShapeEntityAABB; }
    
    public EmitterLocalSpace getEmitterLocalSpace() { return emitterLocalSpace; }
    public void setEmitterLocalSpace(EmitterLocalSpace emitterLocalSpace) { this.emitterLocalSpace = emitterLocalSpace; }
    
    public EmitterInitialization getEmitterInitialization() { return emitterInitialization; }
    public void setEmitterInitialization(EmitterInitialization emitterInitialization) { this.emitterInitialization = emitterInitialization; }
    
    // 粒子组件的getter和setter
    public ParticleInitialSpeed getParticleInitialSpeed() { return particleInitialSpeed; }
    public void setParticleInitialSpeed(ParticleInitialSpeed particleInitialSpeed) { this.particleInitialSpeed = particleInitialSpeed; }
    
    public ParticleInitialSpin getParticleInitialSpin() { return particleInitialSpin; }
    public void setParticleInitialSpin(ParticleInitialSpin particleInitialSpin) { this.particleInitialSpin = particleInitialSpin; }
    
    public ParticleLifetimeExpression getParticleLifetimeExpression() { return particleLifetimeExpression; }
    public void setParticleLifetimeExpression(ParticleLifetimeExpression particleLifetimeExpression) { this.particleLifetimeExpression = particleLifetimeExpression; }
    
    public ParticleLifetimeEvents getParticleLifetimeEvents() { return particleLifetimeEvents; }
    public void setParticleLifetimeEvents(ParticleLifetimeEvents particleLifetimeEvents) { this.particleLifetimeEvents = particleLifetimeEvents; }
    
    public ParticleMotionDynamic getParticleMotionDynamic() { return particleMotionDynamic; }
    public void setParticleMotionDynamic(ParticleMotionDynamic particleMotionDynamic) { this.particleMotionDynamic = particleMotionDynamic; }
    
    public ParticleMotionParametric getParticleMotionParametric() { return particleMotionParametric; }
    public void setParticleMotionParametric(ParticleMotionParametric particleMotionParametric) { this.particleMotionParametric = particleMotionParametric; }
    
    public ParticleMotionCollision getParticleMotionCollision() { return particleMotionCollision; }
    public void setParticleMotionCollision(ParticleMotionCollision particleMotionCollision) { this.particleMotionCollision = particleMotionCollision; }
    
    public ParticleAppearanceBillboard getParticleAppearanceBillboard() { return particleAppearanceBillboard; }
    public void setParticleAppearanceBillboard(ParticleAppearanceBillboard particleAppearanceBillboard) { this.particleAppearanceBillboard = particleAppearanceBillboard; }
    
    public ParticleAppearanceTinting getParticleAppearanceTinting() { return particleAppearanceTinting; }
    public void setParticleAppearanceTinting(ParticleAppearanceTinting particleAppearanceTinting) { this.particleAppearanceTinting = particleAppearanceTinting; }
    
    public ParticleAppearanceLighting getParticleAppearanceLighting() { return particleAppearanceLighting; }
    public void setParticleAppearanceLighting(ParticleAppearanceLighting particleAppearanceLighting) { this.particleAppearanceLighting = particleAppearanceLighting; }
    
    public ParticleExpireIfInBlocks getParticleExpireIfInBlocks() { return particleExpireIfInBlocks; }
    public void setParticleExpireIfInBlocks(ParticleExpireIfInBlocks particleExpireIfInBlocks) { this.particleExpireIfInBlocks = particleExpireIfInBlocks; }
    
    public ParticleExpireIfNotInBlocks getParticleExpireIfNotInBlocks() { return particleExpireIfNotInBlocks; }
    public void setParticleExpireIfNotInBlocks(ParticleExpireIfNotInBlocks particleExpireIfNotInBlocks) { this.particleExpireIfNotInBlocks = particleExpireIfNotInBlocks; }
    
    public ParticleKillPlane getParticleKillPlane() { return particleKillPlane; }
    public void setParticleKillPlane(ParticleKillPlane particleKillPlane) { this.particleKillPlane = particleKillPlane; }
}
