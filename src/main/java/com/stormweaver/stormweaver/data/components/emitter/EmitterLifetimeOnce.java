package com.stormweaver.stormweaver.data.components.emitter;

import com.google.gson.annotations.SerializedName;

/**
 * 发射器一次性生命周期组件
 * 
 * 对应基岩版的 minecraft:emitter_lifetime_once 组件
 * 发射器执行一次，当生命周期结束或允许发射的粒子数量已发射完毕时，发射器过期
 * 
 * JSON示例:
 * {
 *   "minecraft:emitter_lifetime_once": {
 *     "active_time": 5.0
 *   }
 * }
 * 
 * <AUTHOR>
 */
public class EmitterLifetimeOnce {
    
    /**
     * 粒子发射持续时间
     * 可以是常量或MoLang表达式
     * 求值一次
     * 默认值: 10
     */
    @SerializedName("active_time")
    private String activeTime = "10";
    
    /**
     * 默认构造函数
     */
    public EmitterLifetimeOnce() {
    }
    
    /**
     * 构造函数
     * 
     * @param activeTime 活跃时间
     */
    public EmitterLifetimeOnce(String activeTime) {
        this.activeTime = activeTime != null ? activeTime : "10";
    }
    
    /**
     * 获取活跃时间表达式
     * 
     * @return 活跃时间表达式
     */
    public String getActiveTime() {
        return activeTime;
    }
    
    /**
     * 设置活跃时间表达式
     * 
     * @param activeTime 活跃时间表达式
     */
    public void setActiveTime(String activeTime) {
        this.activeTime = activeTime != null ? activeTime : "10";
    }
    
    /**
     * 验证组件数据的有效性
     * 
     * @return 如果数据有效返回true
     */
    public boolean isValid() {
        return activeTime != null && !activeTime.trim().isEmpty();
    }
    
    @Override
    public String toString() {
        return "EmitterLifetimeOnce{" +
                "activeTime='" + activeTime + '\'' +
                '}';
    }
}
