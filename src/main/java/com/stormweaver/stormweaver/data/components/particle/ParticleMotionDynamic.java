package com.stormweaver.stormweaver.data.components.particle;

import com.google.gson.annotations.SerializedName;

/**
 * 粒子动态运动组件
 * 
 * 对应基岩版的 minecraft:particle_motion_dynamic 组件
 * 从模拟角度指定粒子的动态属性，控制作用在粒子上的力
 * 这些动力学改变粒子的速度，速度是粒子方向和速度的组合
 * 
 * JSON示例:
 * {
 *   "minecraft:particle_motion_dynamic": {
 *     "linear_acceleration": [0, -9.8, 0],
 *     "linear_drag_coefficient": 0.1,
 *     "rotation_acceleration": 0,
 *     "rotation_drag_coefficient": 0
 *   }
 * }
 * 
 * <AUTHOR>
 */
public class ParticleMotionDynamic {
    
    /**
     * 应用于粒子的线性加速度，默认为 [0, 0, 0]
     * 单位：方块/秒²
     * 例如重力为 [0, -9.8, 0]
     * 每帧求值
     */
    @SerializedName("linear_acceleration")
    private String[] linearAcceleration = {"0", "0", "0"};
    
    /**
     * 线性阻力系数
     * 使用方程：acceleration = -linear_drag_coefficient * velocity
     * 其中velocity是当前方向乘以速度
     * 可以理解为空气阻力，值越高阻力越大
     * 每帧求值
     * 默认值：0
     */
    @SerializedName("linear_drag_coefficient")
    private String linearDragCoefficient = "0";
    
    /**
     * 应用于粒子旋转速度的加速度
     * 可以理解为圆盘加速旋转或烟雾开始旋转但随时间减慢
     * 每帧求值
     * 加速度单位：度/秒²
     * 默认值：0
     */
    @SerializedName("rotation_acceleration")
    private String rotationAcceleration = "0";
    
    /**
     * 应用于阻止旋转的阻力
     * 方程：rotation_acceleration += -rotation_rate * rotation_drag_coefficient
     * 用于减慢旋转或限制旋转加速度
     * 可以理解为圆盘加速（acceleration）但达到终端速度（drag）
     * 另一个用途是如果粒子尺寸增长，由于阻力旋转减慢可以增加粒子运动的"重量感"
     * 默认值：0
     */
    @SerializedName("rotation_drag_coefficient")
    private String rotationDragCoefficient = "0";
    
    /**
     * 默认构造函数
     */
    public ParticleMotionDynamic() {
    }
    
    /**
     * 构造函数
     * 
     * @param linearAcceleration 线性加速度
     */
    public ParticleMotionDynamic(String[] linearAcceleration) {
        this.linearAcceleration = linearAcceleration != null ? linearAcceleration : new String[]{"0", "0", "0"};
    }
    
    /**
     * 构造函数
     * 
     * @param linearAcceleration 线性加速度
     * @param linearDragCoefficient 线性阻力系数
     */
    public ParticleMotionDynamic(String[] linearAcceleration, String linearDragCoefficient) {
        this.linearAcceleration = linearAcceleration != null ? linearAcceleration : new String[]{"0", "0", "0"};
        this.linearDragCoefficient = linearDragCoefficient != null ? linearDragCoefficient : "0";
    }
    
    /**
     * 获取线性加速度表达式数组
     * 
     * @return 线性加速度表达式数组 [x, y, z]
     */
    public String[] getLinearAcceleration() {
        return linearAcceleration;
    }
    
    /**
     * 设置线性加速度表达式数组
     * 
     * @param linearAcceleration 线性加速度表达式数组 [x, y, z]
     */
    public void setLinearAcceleration(String[] linearAcceleration) {
        this.linearAcceleration = linearAcceleration != null ? linearAcceleration : new String[]{"0", "0", "0"};
    }
    
    /**
     * 获取线性阻力系数表达式
     * 
     * @return 线性阻力系数表达式
     */
    public String getLinearDragCoefficient() {
        return linearDragCoefficient;
    }
    
    /**
     * 设置线性阻力系数表达式
     * 
     * @param linearDragCoefficient 线性阻力系数表达式
     */
    public void setLinearDragCoefficient(String linearDragCoefficient) {
        this.linearDragCoefficient = linearDragCoefficient != null ? linearDragCoefficient : "0";
    }
    
    /**
     * 获取旋转加速度表达式
     * 
     * @return 旋转加速度表达式
     */
    public String getRotationAcceleration() {
        return rotationAcceleration;
    }
    
    /**
     * 设置旋转加速度表达式
     * 
     * @param rotationAcceleration 旋转加速度表达式
     */
    public void setRotationAcceleration(String rotationAcceleration) {
        this.rotationAcceleration = rotationAcceleration != null ? rotationAcceleration : "0";
    }
    
    /**
     * 获取旋转阻力系数表达式
     * 
     * @return 旋转阻力系数表达式
     */
    public String getRotationDragCoefficient() {
        return rotationDragCoefficient;
    }
    
    /**
     * 设置旋转阻力系数表达式
     * 
     * @param rotationDragCoefficient 旋转阻力系数表达式
     */
    public void setRotationDragCoefficient(String rotationDragCoefficient) {
        this.rotationDragCoefficient = rotationDragCoefficient != null ? rotationDragCoefficient : "0";
    }
    
    /**
     * 验证组件数据的有效性
     * 
     * @return 如果数据有效返回true
     */
    public boolean isValid() {
        // 检查线性加速度数组
        if (linearAcceleration == null || linearAcceleration.length != 3) {
            return false;
        }
        
        for (String coord : linearAcceleration) {
            if (coord == null || coord.trim().isEmpty()) {
                return false;
            }
        }
        
        // 检查其他字段
        return linearDragCoefficient != null && !linearDragCoefficient.trim().isEmpty() &&
               rotationAcceleration != null && !rotationAcceleration.trim().isEmpty() &&
               rotationDragCoefficient != null && !rotationDragCoefficient.trim().isEmpty();
    }
    
    @Override
    public String toString() {
        return "ParticleMotionDynamic{" +
                "linearAcceleration=" + java.util.Arrays.toString(linearAcceleration) +
                ", linearDragCoefficient='" + linearDragCoefficient + '\'' +
                ", rotationAcceleration='" + rotationAcceleration + '\'' +
                ", rotationDragCoefficient='" + rotationDragCoefficient + '\'' +
                '}';
    }
}
