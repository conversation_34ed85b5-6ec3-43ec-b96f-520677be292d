package com.stormweaver.stormweaver.data.components.particle;

import com.google.gson.annotations.SerializedName;

/**
 * 粒子广告牌外观组件
 * 
 * 对应基岩版的 minecraft:particle_appearance_billboard 组件
 * 告诉粒子系统将粒子渲染为广告牌，即世界中面向特定方向的矩形
 * 
 * JSON示例:
 * {
 *   "minecraft:particle_appearance_billboard": {
 *     "size": [0.1, 0.1],
 *     "facing_camera_mode": "lookat_xyz",
 *     "uv": {
 *       "texture_width": 128,
 *       "texture_height": 128,
 *       "uv": [0, 0],
 *       "uv_size": [8, 8]
 *     }
 *   }
 * }
 * 
 * <AUTHOR>
 */
public class ParticleAppearanceBillboard {
    
    /**
     * 指定广告牌的x/y尺寸
     * 每帧求值
     */
    private String[] size = {"0.1", "0.1"};
    
    /**
     * 用于定向广告牌的模式
     * 选项包括：
     * - "rotate_xyz" - 对齐到相机，垂直于视图轴
     * - "rotate_y" - 对齐到相机，但围绕世界y轴旋转
     * - "lookat_xyz" - 瞄准相机，偏向世界y向上
     * - "lookat_y" - 瞄准相机，但围绕世界y轴旋转
     * - "direction_x" - 未旋转的粒子x轴沿方向向量，未旋转的y轴尝试向上瞄准
     * - "direction_y" - 未旋转的粒子y轴沿方向向量，未旋转的x轴尝试向上瞄准
     * - "direction_z" - 广告牌面沿方向向量，未旋转的y轴尝试向上瞄准
     * - "emitter_transform_xy" - 定向粒子以匹配发射器的变换（广告牌平面将匹配变换的xy平面）
     * - "emitter_transform_xz" - 定向粒子以匹配发射器的变换（广告牌平面将匹配变换的xz平面）
     * - "emitter_transform_yz" - 定向粒子以匹配发射器的变换（广告牌平面将匹配变换的yz平面）
     */
    @SerializedName("facing_camera_mode")
    private String facingCameraMode = "rotate_xyz";
    
    /**
     * 方向计算设置
     * 指定如何计算粒子的方向，这将被需要方向作为输入的面向模式使用
     */
    private DirectionSettings direction;
    
    /**
     * UV坐标设置
     * 指定粒子的UV坐标
     */
    private UVSettings uv;
    
    /**
     * 默认构造函数
     */
    public ParticleAppearanceBillboard() {
    }
    
    /**
     * 构造函数
     * 
     * @param size 粒子尺寸
     */
    public ParticleAppearanceBillboard(String[] size) {
        this.size = size != null ? size : new String[]{"0.1", "0.1"};
    }
    
    /**
     * 获取粒子尺寸表达式数组
     * 
     * @return 尺寸表达式数组 [width, height]
     */
    public String[] getSize() {
        return size;
    }
    
    /**
     * 设置粒子尺寸表达式数组
     * 
     * @param size 尺寸表达式数组 [width, height]
     */
    public void setSize(String[] size) {
        this.size = size != null ? size : new String[]{"0.1", "0.1"};
    }
    
    /**
     * 获取面向相机模式
     * 
     * @return 面向相机模式
     */
    public String getFacingCameraMode() {
        return facingCameraMode;
    }
    
    /**
     * 设置面向相机模式
     * 
     * @param facingCameraMode 面向相机模式
     */
    public void setFacingCameraMode(String facingCameraMode) {
        this.facingCameraMode = facingCameraMode != null ? facingCameraMode : "rotate_xyz";
    }
    
    /**
     * 获取方向设置
     * 
     * @return 方向设置
     */
    public DirectionSettings getDirection() {
        return direction;
    }
    
    /**
     * 设置方向设置
     * 
     * @param direction 方向设置
     */
    public void setDirection(DirectionSettings direction) {
        this.direction = direction;
    }
    
    /**
     * 获取UV设置
     * 
     * @return UV设置
     */
    public UVSettings getUv() {
        return uv;
    }
    
    /**
     * 设置UV设置
     * 
     * @param uv UV设置
     */
    public void setUv(UVSettings uv) {
        this.uv = uv;
    }
    
    /**
     * 验证组件数据的有效性
     * 
     * @return 如果数据有效返回true
     */
    public boolean isValid() {
        if (size == null || size.length != 2) {
            return false;
        }
        
        for (String dimension : size) {
            if (dimension == null || dimension.trim().isEmpty()) {
                return false;
            }
        }
        
        if (facingCameraMode == null || facingCameraMode.trim().isEmpty()) {
            return false;
        }
        
        // 验证面向模式是否有效
        String[] validModes = {
            "rotate_xyz", "rotate_y", "lookat_xyz", "lookat_y",
            "direction_x", "direction_y", "direction_z",
            "emitter_transform_xy", "emitter_transform_xz", "emitter_transform_yz"
        };
        
        boolean validMode = false;
        for (String mode : validModes) {
            if (mode.equals(facingCameraMode)) {
                validMode = true;
                break;
            }
        }
        
        if (!validMode) {
            return false;
        }
        
        // 验证方向设置（如果存在）
        if (direction != null && !direction.isValid()) {
            return false;
        }
        
        // 验证UV设置（如果存在）
        if (uv != null && !uv.isValid()) {
            return false;
        }
        
        return true;
    }
    
    /**
     * 方向设置内部类
     */
    public static class DirectionSettings {
        
        /**
         * 方向模式
         * 选项：
         * - "derive_from_velocity" - 方向匹配速度方向
         * - "custom_direction" - 方向在JSON定义中使用浮点数或MoLang表达式向量指定
         */
        private String mode = "derive_from_velocity";
        
        /**
         * 最小速度阈值
         * 仅在"derive_from_velocity"模式中使用
         * 如果粒子速度超过阈值则设置方向
         * 默认值：0.01
         */
        @SerializedName("min_speed_threshold")
        private String minSpeedThreshold = "0.01";
        
        /**
         * 自定义方向
         * 仅在"custom_direction"模式中使用
         * 指定方向向量
         */
        @SerializedName("custom_direction")
        private String[] customDirection;
        
        public String getMode() { return mode; }
        public void setMode(String mode) { this.mode = mode; }
        
        public String getMinSpeedThreshold() { return minSpeedThreshold; }
        public void setMinSpeedThreshold(String minSpeedThreshold) { this.minSpeedThreshold = minSpeedThreshold; }
        
        public String[] getCustomDirection() { return customDirection; }
        public void setCustomDirection(String[] customDirection) { this.customDirection = customDirection; }
        
        public boolean isValid() {
            if (mode == null) return false;
            
            if ("derive_from_velocity".equals(mode)) {
                return minSpeedThreshold != null && !minSpeedThreshold.trim().isEmpty();
            } else if ("custom_direction".equals(mode)) {
                if (customDirection == null || customDirection.length != 3) return false;
                for (String coord : customDirection) {
                    if (coord == null || coord.trim().isEmpty()) return false;
                }
                return true;
            }
            
            return false;
        }
    }
    
    /**
     * UV设置内部类
     */
    public static class UVSettings {
        
        /**
         * 假定的纹理宽度
         * 默认为1，当设置为1时UV工作就像标准化UV
         * 当设置为纹理宽度/高度时，这像纹素一样工作
         */
        @SerializedName("texture_width")
        private int textureWidth = 1;
        
        /**
         * 假定的纹理高度
         */
        @SerializedName("texture_height")
        private int textureHeight = 1;
        
        /**
         * UV坐标
         * 每帧求值
         */
        private String[] uv = {"0", "0"};
        
        /**
         * UV尺寸
         * 每帧求值
         */
        @SerializedName("uv_size")
        private String[] uvSize = {"1", "1"};
        
        /**
         * 翻页动画设置
         * 翻页动画使用纹理片段随时间动画化，通过从一个"帧"步进到另一个
         */
        private FlipbookSettings flipbook;
        
        // Getters and Setters
        public int getTextureWidth() { return textureWidth; }
        public void setTextureWidth(int textureWidth) { this.textureWidth = textureWidth; }
        
        public int getTextureHeight() { return textureHeight; }
        public void setTextureHeight(int textureHeight) { this.textureHeight = textureHeight; }
        
        public String[] getUv() { return uv; }
        public void setUv(String[] uv) { this.uv = uv; }
        
        public String[] getUvSize() { return uvSize; }
        public void setUvSize(String[] uvSize) { this.uvSize = uvSize; }
        
        public FlipbookSettings getFlipbook() { return flipbook; }
        public void setFlipbook(FlipbookSettings flipbook) { this.flipbook = flipbook; }
        
        public boolean isValid() {
            if (textureWidth <= 0 || textureHeight <= 0) return false;
            
            if (uv == null || uv.length != 2) return false;
            for (String coord : uv) {
                if (coord == null || coord.trim().isEmpty()) return false;
            }
            
            if (uvSize == null || uvSize.length != 2) return false;
            for (String size : uvSize) {
                if (size == null || size.trim().isEmpty()) return false;
            }
            
            if (flipbook != null && !flipbook.isValid()) return false;
            
            return true;
        }
    }
    
    /**
     * 翻页动画设置内部类
     */
    public static class FlipbookSettings {
        
        /**
         * 起始UV补丁的左上角
         */
        @SerializedName("base_UV")
        private String[] baseUV = {"0", "0"};
        
        /**
         * UV补丁的尺寸
         */
        @SerializedName("size_UV")
        private String[] sizeUV = {"1", "1"};
        
        /**
         * 每帧移动UV补丁的距离
         */
        @SerializedName("step_UV")
        private String[] stepUV = {"1", "0"};
        
        /**
         * 默认每秒帧数
         */
        @SerializedName("frames_per_second")
        private String framesPerSecond = "8";
        
        /**
         * 最大帧数，第一帧为帧1
         */
        @SerializedName("max_frame")
        private String maxFrame = "8";
        
        /**
         * 可选，调整fps以匹配粒子的生命周期
         * 默认：false
         */
        @SerializedName("stretch_to_lifetime")
        private boolean stretchToLifetime = false;
        
        /**
         * 可选，当动画到达结尾时是否循环
         * 默认：false
         */
        private boolean loop = false;
        
        // Getters and Setters
        public String[] getBaseUV() { return baseUV; }
        public void setBaseUV(String[] baseUV) { this.baseUV = baseUV; }
        
        public String[] getSizeUV() { return sizeUV; }
        public void setSizeUV(String[] sizeUV) { this.sizeUV = sizeUV; }
        
        public String[] getStepUV() { return stepUV; }
        public void setStepUV(String[] stepUV) { this.stepUV = stepUV; }
        
        public String getFramesPerSecond() { return framesPerSecond; }
        public void setFramesPerSecond(String framesPerSecond) { this.framesPerSecond = framesPerSecond; }
        
        public String getMaxFrame() { return maxFrame; }
        public void setMaxFrame(String maxFrame) { this.maxFrame = maxFrame; }
        
        public boolean isStretchToLifetime() { return stretchToLifetime; }
        public void setStretchToLifetime(boolean stretchToLifetime) { this.stretchToLifetime = stretchToLifetime; }
        
        public boolean isLoop() { return loop; }
        public void setLoop(boolean loop) { this.loop = loop; }
        
        public boolean isValid() {
            if (baseUV == null || baseUV.length != 2) return false;
            if (sizeUV == null || sizeUV.length != 2) return false;
            if (stepUV == null || stepUV.length != 2) return false;
            
            String[][] arrays = {baseUV, sizeUV, stepUV};
            for (String[] array : arrays) {
                for (String value : array) {
                    if (value == null || value.trim().isEmpty()) return false;
                }
            }
            
            return framesPerSecond != null && !framesPerSecond.trim().isEmpty() &&
                   maxFrame != null && !maxFrame.trim().isEmpty();
        }
    }
    
    @Override
    public String toString() {
        return "ParticleAppearanceBillboard{" +
                "size=" + java.util.Arrays.toString(size) +
                ", facingCameraMode='" + facingCameraMode + '\'' +
                ", direction=" + direction +
                ", uv=" + uv +
                '}';
    }
}
