package com.stormweaver.stormweaver.data.components.emitter;

import com.google.gson.annotations.SerializedName;

/**
 * 发射器球体形状组件
 * 
 * 对应基岩版的 minecraft:emitter_shape_sphere 组件
 * 所有粒子从球体偏移位置发射
 * 
 * JSON示例:
 * {
 *   "minecraft:emitter_shape_sphere": {
 *     "offset": [0, 0, 0],
 *     "radius": 1.0,
 *     "surface_only": false,
 *     "direction": "outwards"
 *   }
 * }
 * 
 * <AUTHOR>
 */
public class EmitterShapeSphere {
    
    /**
     * 从发射器发射粒子的偏移量
     * 可以是常量数组或MoLang表达式数组
     * 默认值: [0, 0, 0]
     */
    private String[] offset = {"0", "0", "0"};
    
    /**
     * 球体半径
     * 可以是常量或MoLang表达式
     * 默认值: 1
     */
    private String radius = "1";
    
    /**
     * 是否只从球体表面发射
     * 默认值: false
     */
    @SerializedName("surface_only")
    private boolean surfaceOnly = false;
    
    /**
     * 粒子方向
     * 可以是预定义字符串或自定义方向数组
     * 预定义值: "inwards", "outwards"
     * 自定义值: [x, y, z] 表达式数组
     */
    private Object direction = "outwards";
    
    /**
     * 默认构造函数
     */
    public EmitterShapeSphere() {
    }
    
    /**
     * 构造函数
     * 
     * @param radius 球体半径
     */
    public EmitterShapeSphere(String radius) {
        this.radius = radius;
    }
    
    /**
     * 构造函数
     * 
     * @param offset 偏移量
     * @param radius 球体半径
     * @param surfaceOnly 是否只从表面发射
     */
    public EmitterShapeSphere(String[] offset, String radius, boolean surfaceOnly) {
        this.offset = offset != null ? offset : new String[]{"0", "0", "0"};
        this.radius = radius;
        this.surfaceOnly = surfaceOnly;
    }
    
    /**
     * 获取偏移量表达式数组
     * 
     * @return 偏移量表达式数组 [x, y, z]
     */
    public String[] getOffset() {
        return offset;
    }
    
    /**
     * 设置偏移量表达式数组
     * 
     * @param offset 偏移量表达式数组 [x, y, z]
     */
    public void setOffset(String[] offset) {
        this.offset = offset != null ? offset : new String[]{"0", "0", "0"};
    }
    
    /**
     * 获取球体半径表达式
     * 
     * @return 球体半径表达式
     */
    public String getRadius() {
        return radius;
    }
    
    /**
     * 设置球体半径表达式
     * 
     * @param radius 球体半径表达式
     */
    public void setRadius(String radius) {
        this.radius = radius != null ? radius : "1";
    }
    
    /**
     * 是否只从球体表面发射
     * 
     * @return 如果只从表面发射返回true
     */
    public boolean isSurfaceOnly() {
        return surfaceOnly;
    }
    
    /**
     * 设置是否只从球体表面发射
     * 
     * @param surfaceOnly 是否只从表面发射
     */
    public void setSurfaceOnly(boolean surfaceOnly) {
        this.surfaceOnly = surfaceOnly;
    }
    
    /**
     * 获取方向设置
     * 
     * @return 方向设置（字符串或数组）
     */
    public Object getDirection() {
        return direction;
    }
    
    /**
     * 设置方向（预定义方向）
     * 
     * @param direction 预定义方向 ("inwards" 或 "outwards")
     */
    public void setDirection(String direction) {
        this.direction = direction != null ? direction : "outwards";
    }
    
    /**
     * 设置方向（自定义方向向量）
     * 
     * @param direction 自定义方向向量 [x, y, z]
     */
    public void setDirection(String[] direction) {
        this.direction = direction != null ? direction : "outwards";
    }
    
    /**
     * 检查方向是否为预定义类型
     * 
     * @return 如果是预定义方向返回true
     */
    public boolean isDirectionPredefined() {
        return direction instanceof String;
    }
    
    /**
     * 获取预定义方向
     * 
     * @return 预定义方向字符串，如果不是预定义方向则返回null
     */
    public String getPredefinedDirection() {
        return isDirectionPredefined() ? (String) direction : null;
    }
    
    /**
     * 获取自定义方向向量
     * 
     * @return 自定义方向向量，如果不是自定义方向则返回null
     */
    public String[] getCustomDirection() {
        return !isDirectionPredefined() ? (String[]) direction : null;
    }
    
    /**
     * 验证组件数据的有效性
     * 
     * @return 如果数据有效返回true
     */
    public boolean isValid() {
        if (offset == null || offset.length != 3) {
            return false;
        }
        
        for (String coord : offset) {
            if (coord == null || coord.trim().isEmpty()) {
                return false;
            }
        }
        
        if (radius == null || radius.trim().isEmpty()) {
            return false;
        }
        
        if (direction == null) {
            return false;
        }
        
        if (direction instanceof String) {
            String dirStr = (String) direction;
            return "inwards".equals(dirStr) || "outwards".equals(dirStr);
        } else if (direction instanceof String[]) {
            String[] dirArray = (String[]) direction;
            if (dirArray.length != 3) {
                return false;
            }
            for (String coord : dirArray) {
                if (coord == null || coord.trim().isEmpty()) {
                    return false;
                }
            }
            return true;
        }
        
        return false;
    }
    
    @Override
    public String toString() {
        return "EmitterShapeSphere{" +
                "offset=" + java.util.Arrays.toString(offset) +
                ", radius='" + radius + '\'' +
                ", surfaceOnly=" + surfaceOnly +
                ", direction=" + (direction instanceof String[] ? 
                    java.util.Arrays.toString((String[]) direction) : direction) +
                '}';
    }
}
