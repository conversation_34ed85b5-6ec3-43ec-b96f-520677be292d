package com.stormweaver.stormweaver.data.components.particle;

import com.google.gson.annotations.SerializedName;

/**
 * 粒子生命周期表达式组件
 * 
 * 对应基岩版的 minecraft:particle_lifetime_expression 组件
 * 标准生命周期组件，这些表达式控制粒子的生命周期
 * 
 * JSON示例:
 * {
 *   "minecraft:particle_lifetime_expression": {
 *     "max_lifetime": 5.0,
 *     "expiration_expression": "variable.particle_age > 3.0"
 *   }
 * }
 * 
 * <AUTHOR>
 */
public class ParticleLifetimeExpression {
    
    /**
     * 粒子的最大生命周期
     * 粒子在此时间后过期
     * 可以是常量或MoLang表达式
     * 求值一次
     */
    @SerializedName("max_lifetime")
    private String maxLifetime;
    
    /**
     * 过期表达式
     * 当此表达式为真（非零）时粒子过期
     * 可以是常量或MoLang表达式
     * 每帧求值
     * 默认值: 0
     */
    @SerializedName("expiration_expression")
    private String expirationExpression = "0";
    
    /**
     * 默认构造函数
     */
    public ParticleLifetimeExpression() {
    }
    
    /**
     * 构造函数
     * 
     * @param maxLifetime 最大生命周期
     */
    public ParticleLifetimeExpression(String maxLifetime) {
        this.maxLifetime = maxLifetime;
    }
    
    /**
     * 构造函数
     * 
     * @param maxLifetime 最大生命周期
     * @param expirationExpression 过期表达式
     */
    public ParticleLifetimeExpression(String maxLifetime, String expirationExpression) {
        this.maxLifetime = maxLifetime;
        this.expirationExpression = expirationExpression != null ? expirationExpression : "0";
    }
    
    /**
     * 获取最大生命周期表达式
     * 
     * @return 最大生命周期表达式
     */
    public String getMaxLifetime() {
        return maxLifetime;
    }
    
    /**
     * 设置最大生命周期表达式
     * 
     * @param maxLifetime 最大生命周期表达式
     */
    public void setMaxLifetime(String maxLifetime) {
        this.maxLifetime = maxLifetime;
    }
    
    /**
     * 获取过期表达式
     * 
     * @return 过期表达式
     */
    public String getExpirationExpression() {
        return expirationExpression;
    }
    
    /**
     * 设置过期表达式
     * 
     * @param expirationExpression 过期表达式
     */
    public void setExpirationExpression(String expirationExpression) {
        this.expirationExpression = expirationExpression != null ? expirationExpression : "0";
    }
    
    /**
     * 验证组件数据的有效性
     * 
     * @return 如果数据有效返回true
     */
    public boolean isValid() {
        // 至少需要有最大生命周期或过期表达式之一
        boolean hasMaxLifetime = maxLifetime != null && !maxLifetime.trim().isEmpty();
        boolean hasExpirationExpression = expirationExpression != null && !expirationExpression.trim().isEmpty();
        
        return hasMaxLifetime || hasExpirationExpression;
    }
    
    @Override
    public String toString() {
        return "ParticleLifetimeExpression{" +
                "maxLifetime='" + maxLifetime + '\'' +
                ", expirationExpression='" + expirationExpression + '\'' +
                '}';
    }
}
