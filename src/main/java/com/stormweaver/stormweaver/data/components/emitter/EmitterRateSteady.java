package com.stormweaver.stormweaver.data.components.emitter;

import com.google.gson.annotations.SerializedName;

/**
 * 发射器稳定速率组件
 * 
 * 对应基岩版的 minecraft:emitter_rate_steady 组件
 * 控制粒子以稳定或MoLang控制的速率随时间发射
 * 
 * JSON示例:
 * {
 *   "minecraft:emitter_rate_steady": {
 *     "spawn_rate": 10,
 *     "max_particles": 100
 *   }
 * }
 * 
 * <AUTHOR>
 */
public class EmitterRateSteady {
    
    /**
     * 粒子生成速率（粒子/秒）
     * 可以是常量或MoLang表达式
     * 默认值: 1
     */
    @SerializedName("spawn_rate")
    private String spawnRate = "1";
    
    /**
     * 此发射器同时活跃的最大粒子数量
     * 可以是常量或MoLang表达式
     * 默认值: 50
     */
    @SerializedName("max_particles")
    private String maxParticles = "50";
    
    /**
     * 默认构造函数
     */
    public EmitterRateSteady() {
    }
    
    /**
     * 构造函数
     * 
     * @param spawnRate 生成速率
     * @param maxParticles 最大粒子数
     */
    public EmitterRateSteady(String spawnRate, String maxParticles) {
        this.spawnRate = spawnRate;
        this.maxParticles = maxParticles;
    }
    
    /**
     * 获取生成速率表达式
     * 
     * @return 生成速率表达式
     */
    public String getSpawnRate() {
        return spawnRate;
    }
    
    /**
     * 设置生成速率表达式
     * 
     * @param spawnRate 生成速率表达式
     */
    public void setSpawnRate(String spawnRate) {
        this.spawnRate = spawnRate != null ? spawnRate : "1";
    }
    
    /**
     * 获取最大粒子数表达式
     * 
     * @return 最大粒子数表达式
     */
    public String getMaxParticles() {
        return maxParticles;
    }
    
    /**
     * 设置最大粒子数表达式
     * 
     * @param maxParticles 最大粒子数表达式
     */
    public void setMaxParticles(String maxParticles) {
        this.maxParticles = maxParticles != null ? maxParticles : "50";
    }
    
    /**
     * 验证组件数据的有效性
     * 
     * @return 如果数据有效返回true
     */
    public boolean isValid() {
        return spawnRate != null && !spawnRate.trim().isEmpty() &&
               maxParticles != null && !maxParticles.trim().isEmpty();
    }
    
    @Override
    public String toString() {
        return "EmitterRateSteady{" +
                "spawnRate='" + spawnRate + '\'' +
                ", maxParticles='" + maxParticles + '\'' +
                '}';
    }
}
