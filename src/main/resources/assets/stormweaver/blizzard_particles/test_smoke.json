{"format_version": "1.10.0", "particle_effect": {"description": {"identifier": "stormweaver:test_smoke", "basic_render_parameters": {"material": "particles_alpha", "texture": "textures/particle/generic_0"}}, "components": {"minecraft:emitter_rate_steady": {"spawn_rate": 5, "max_particles": 50}, "minecraft:emitter_lifetime_once": {"active_time": 3.0}, "minecraft:emitter_shape_sphere": {"offset": [0, 0, 0], "radius": 0.5, "surface_only": false, "direction": "outwards"}, "minecraft:particle_lifetime_expression": {"max_lifetime": "Math.random(1.0, 3.0)"}, "minecraft:particle_motion_dynamic": {"linear_acceleration": [0, 0.5, 0], "linear_drag_coefficient": 0.1}, "minecraft:particle_appearance_billboard": {"size": ["0.1 + variable.particle_age * 0.05", "0.1 + variable.particle_age * 0.05"], "facing_camera_mode": "lookat_xyz"}}}}