@echo off
echo ========================================
echo StormWeaver 项目设置脚本
echo ========================================
echo.

echo 正在检查Java版本...
java -version
if %errorlevel% neq 0 (
    echo 错误: 未找到Java，请确保已安装Java 17
    pause
    exit /b 1
)

echo.
echo 正在检查Gradle...
gradle --version
if %errorlevel% neq 0 (
    echo 警告: 未找到本地Gradle，将尝试使用Gradle Wrapper
)

echo.
echo 正在清理项目...
if exist build rmdir /s /q build
if exist .gradle rmdir /s /q .gradle

echo.
echo 正在设置Gradle Wrapper...
if not exist gradle\wrapper\gradle-wrapper.jar (
    echo 下载Gradle Wrapper...
    gradle wrapper --gradle-version 8.1.1
    if %errorlevel% neq 0 (
        echo 错误: 无法设置Gradle Wrapper
        echo 请手动下载Gradle 8.1.1并配置
        pause
        exit /b 1
    )
)

echo.
echo 正在构建项目...
gradlew build --no-daemon
if %errorlevel% neq 0 (
    echo 警告: 构建失败，但项目结构已创建
    echo 您可以在IDE中导入项目并手动解决依赖问题
)

echo.
echo ========================================
echo 设置完成！
echo ========================================
echo.
echo 下一步：
echo 1. 在IDE中打开项目文件夹: %CD%
echo 2. 导入为Gradle项目
echo 3. 等待依赖下载完成
echo 4. 运行 runClient 任务测试
echo.
echo 详细导入指南请查看: IDE_IMPORT_GUIDE.md
echo.
pause
