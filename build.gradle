buildscript {
    repositories {
        // These repositories are only for <PERSON><PERSON><PERSON> plugins, put any other repositories in the repository block further below
        maven { url = 'https://maven.minecraftforge.net' }
        mavenCentral()
    }
    dependencies {
        classpath group: 'net.minecraftforge.gradle', name: 'ForgeGradle', version: '5.1.+', changing: true
        classpath 'org.parchmentmc:librarian:1.+' // Optional, used to run tasks in IDE
    }
}

plugins {
    id 'eclipse'
    id 'maven-publish'
    id 'net.minecraftforge.gradle' version '5.1.+'
    id 'org.parchmentmc.librarian.forgegradle' version '1.+' // Optional, used to run tasks in IDE
}

version = '1.0.0'
group = 'com.stormweaver.stormweaver' // http://maven.apache.org/guides/mini/guide-naming-conventions.html
archivesBaseName = 'stormweaver'

// Mojang ships Java 17 to end users in 1.18+, so your mod should target Java 17.
java.toolchain.languageVersion = JavaLanguageVersion.of(17)

println "Java: ${System.getProperty 'java.version'}, JVM: ${System.getProperty 'java.vm.version'} (${System.getProperty 'java.vendor'}), Arch: ${System.getProperty 'os.arch'}"
minecraft {
    // The mappings can be changed at any time and must be in the following format.
    // Channel:   Version:
    // official   MCVersion             Official field/method names from Mojang mapping files
    // parchment  YYYY.MM.DD-MCVersion  Open community-sourced parameter names and javadocs layered on top of official

    mappings channel: 'official', version: '1.20.1'

    // accessTransformer = file('src/main/resources/META-INF/accesstransformer.cfg') // Currently, this location cannot be changed from the default.

    // Default run configurations.
    // These can be tweaked, removed, or duplicated as needed.
    runs {
        client {
            workingDirectory project.file('run')

            // Recommended logging data for a userdev environment
            // The markers can be added/remove as needed separated by commas.
            // "SCAN": For mods scan.
            // "REGISTRIES": For firing of registry events.
            // "REGISTRYDUMP": For getting the contents of all registries.
            property 'forge.logging.markers', 'REGISTRIES'

            // Recommended logging level for the console
            // You can set various levels here.
            // Please read: https://stackoverflow.com/questions/2031163/when-to-use-the-different-log-levels
            property 'forge.logging.console.level', 'debug'

            // Comma-separated list of namespaces to load gametests from. Empty = all namespaces.
            property 'forge.enabledGameTestNamespaces', 'stormweaver'

            mods {
                stormweaver {
                    source sourceSets.main
                }
            }
        }

        server {
            workingDirectory project.file('run')

            property 'forge.logging.markers', 'REGISTRIES'

            property 'forge.logging.console.level', 'debug'

            property 'forge.enabledGameTestNamespaces', 'stormweaver'

            mods {
                stormweaver {
                    source sourceSets.main
                }
            }
        }

        // This run config launches GameTestServer and runs all registered gametests, then exits.
        // By default, the server will crash when no gametests are provided.
        // The gametest system is also enabled by default for other run configs under the /test command.
        gameTestServer {
            workingDirectory project.file('run')

            property 'forge.logging.markers', 'REGISTRIES'

            property 'forge.logging.console.level', 'debug'

            property 'forge.enabledGameTestNamespaces', 'stormweaver'

            mods {
                stormweaver {
                    source sourceSets.main
                }
            }
        }

        data {
            workingDirectory project.file('run')

            property 'forge.logging.markers', 'REGISTRIES'

            property 'forge.logging.console.level', 'debug'

            // Specify the modid for data generation, where to output the resulting resource, and where to look for existing resources.
            args '--mod', 'stormweaver', '--all', '--output', file('src/generated/resources/'), '--existing', file('src/main/resources/')

            mods {
                stormweaver {
                    source sourceSets.main
                }
            }
        }
    }
}

// Include resources generated by data generators.
sourceSets.main.resources { srcDir 'src/generated/resources' }

repositories {
    // Put repositories for dependencies here
    // ForgeGradle automatically adds the Forge maven and Maven Central for you

    // If you have mod jar dependencies in ./libs, you can declare them as a repository like so:
    // flatDir {
    //     dir 'libs'
    // }
    mavenCentral()
}

dependencies {
    // Specify the version of Minecraft to use. If this is any group other than 'net.minecraft', it is assumed
    // that the dep is a ForgeGradle 'patcher' dependency, and its patches will be applied.
    // The userdev artifact is a special name and will get all sorts of transformations applied to it.
    minecraft 'net.minecraftforge:forge:1.20.1-47.2.0'

    // Real mod deobf dependency examples - these get remapped to your current mappings
    // compileOnly fg.deobf("mezz.jei:jei-${mc_version}:${jei_version}:api") // Adds JEI API as a compile dependency
    // runtimeOnly fg.deobf("mezz.jei:jei-${mc_version}:${jei_version}") // Adds the full JEI mod as a runtime dependency
    // implementation fg.deobf("com.tterrag.registrate:Registrate:MC${mc_version}-${registrate_version}") // Adds registrate as a dependency

    // Examples using mod jars from ./libs
    // implementation fg.deobf("blank:coolmod-${mc_version}:${coolmod_version}")

    // For more info...
    // http://www.gradle.org/docs/current/userguide/artifact_dependencies_tutorial.html
    // http://www.gradle.org/docs/current/userguide/dependency_management.html

    // 添加exp4j数学表达式求值库
    implementation 'net.objecthunter:exp4j:0.4.8'
    
    // 添加Gson JSON解析库
    implementation 'com.google.code.gson:gson:2.10.1'
}

// Example for how to get properties into the manifest for reading at runtime.
jar {
    manifest {
        attributes([
                "Specification-Title"     : "stormweaver",
                "Specification-Vendor"   : "stormweaversareus",
                "Specification-Version"  : "1", // We are version 1 of ourselves
                "Implementation-Title"   : project.name,
                "Implementation-Version" : project.jar.archiveVersion,
                "Implementation-Vendor"  : "stormweaversareus",
                "Implementation-Timestamp": new Date().format("yyyy-MM-dd'T'HH:mm:ssZ")
        ])
    }
}

// Example configuration to allow publishing using the maven-publish plugin
// This is the preferred method to reobfuscate your jar file
jar.finalizedBy('reobfJar')

publishing {
    publications {
        maven(MavenPublication) {
            artifact jar
        }
    }
    repositories {
        maven {
            url "file://${project.projectDir}/mcmodsrepo"
        }
    }
}

tasks.withType(JavaCompile).configureEach {
    options.encoding = 'UTF-8' // Use the UTF-8 charset for Java compilation
}
