plugins {
    id 'eclipse'
    id 'maven-publish'
    id 'net.minecraftforge.gradle' version '5.1.53'
}

version = '1.0.0'
group = 'com.stormweaver.stormweaver'
base.archivesName = 'stormweaver'

java.toolchain.languageVersion = JavaLanguageVersion.of(17)

println "Java: ${System.getProperty 'java.version'}, JVM: ${System.getProperty 'java.vm.version'} (${System.getProperty 'java.vendor'}), Arch: ${System.getProperty 'os.arch'}"

minecraft {
    mappings channel: 'official', version: '1.20.1'

    runs {
        client {
            workingDirectory project.file('run')
            property 'forge.logging.markers', 'REGISTRIES'
            property 'forge.logging.console.level', 'debug'
            property 'forge.enabledGameTestNamespaces', 'stormweaver'

            mods {
                stormweaver {
                    source sourceSets.main
                }
            }
        }

        server {
            workingDirectory project.file('run')
            property 'forge.logging.markers', 'REGISTRIES'
            property 'forge.logging.console.level', 'debug'
            property 'forge.enabledGameTestNamespaces', 'stormweaver'

            mods {
                stormweaver {
                    source sourceSets.main
                }
            }
        }

        gameTestServer {
            workingDirectory project.file('run')
            property 'forge.logging.markers', 'REGISTRIES'
            property 'forge.logging.console.level', 'debug'
            property 'forge.enabledGameTestNamespaces', 'stormweaver'

            mods {
                stormweaver {
                    source sourceSets.main
                }
            }
        }

        data {
            workingDirectory project.file('run')
            property 'forge.logging.markers', 'REGISTRIES'
            property 'forge.logging.console.level', 'debug'
            args '--mod', 'stormweaver', '--all', '--output', file('src/generated/resources/'), '--existing', file('src/main/resources/')

            mods {
                stormweaver {
                    source sourceSets.main
                }
            }
        }
    }
}

sourceSets.main.resources { srcDir 'src/generated/resources' }

repositories {
    mavenCentral()
}

dependencies {
    minecraft 'net.minecraftforge:forge:1.20.1-47.2.0'
    
    // 添加exp4j数学表达式求值库
    implementation 'net.objecthunter:exp4j:0.4.8'
    
    // 添加Gson JSON解析库
    implementation 'com.google.code.gson:gson:2.10.1'
}

jar {
    manifest {
        attributes([
                "Specification-Title"     : "stormweaver",
                "Specification-Vendor"   : "stormweaversareus",
                "Specification-Version"  : "1",
                "Implementation-Title"   : project.name,
                "Implementation-Version" : project.jar.archiveVersion,
                "Implementation-Vendor"  : "stormweaversareus",
                "Implementation-Timestamp": new Date().format("yyyy-MM-dd'T'HH:mm:ssZ")
        ])
    }
}

jar.finalizedBy('reobfJar')

publishing {
    publications {
        maven(MavenPublication) {
            artifact jar
        }
    }
    repositories {
        maven {
            url "file://${project.projectDir}/mcmodsrepo"
        }
    }
}

tasks.withType(JavaCompile).configureEach {
    options.encoding = 'UTF-8'
}
