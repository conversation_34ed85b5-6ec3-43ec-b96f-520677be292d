@echo off
echo ========================================
echo StormWeaver Gradle 修复脚本
echo ========================================
echo.

echo 正在清理Gradle缓存...
if exist .gradle rmdir /s /q .gradle
if exist build rmdir /s /q build

echo.
echo 正在清理用户Gradle缓存...
if exist "%USERPROFILE%\.gradle\caches" (
    echo 清理用户Gradle缓存目录...
    rmdir /s /q "%USERPROFILE%\.gradle\caches"
)

echo.
echo 正在重新初始化Gradle Wrapper...
if exist gradle\wrapper rmdir /s /q gradle\wrapper
mkdir gradle\wrapper

echo distributionBase=GRADLE_USER_HOME > gradle\wrapper\gradle-wrapper.properties
echo distributionPath=wrapper/dists >> gradle\wrapper\gradle-wrapper.properties
echo distributionUrl=https\://services.gradle.org/distributions/gradle-8.1.1-bin.zip >> gradle\wrapper\gradle-wrapper.properties
echo networkTimeout=10000 >> gradle\wrapper\gradle-wrapper.properties
echo zipStoreBase=GRADLE_USER_HOME >> gradle\wrapper\gradle-wrapper.properties
echo zipStorePath=wrapper/dists >> gradle\wrapper\gradle-wrapper.properties

echo.
echo ========================================
echo 修复完成！
echo ========================================
echo.
echo 现在可以在IDE中重新导入项目：
echo 1. 关闭IDE中的项目
echo 2. 重新打开项目文件夹
echo 3. 选择 build.gradle 文件
echo 4. 选择 "Open as Project"
echo.
echo 如果还有问题，请尝试：
echo - 重启IDE
echo - 检查网络连接
echo - 确保使用Java 17
echo.
pause
