

# **为Minecraft 1.20.1 Forge实现暴雪引擎粒子JSON文件的技术方案**

## **第一部分：体系结构概述与“暴雪引擎”粒子格式分析**

本报告旨在为在Minecraft 1.20.1 Forge环境下兼容并渲染基于“暴雪引擎”JSON文件的粒子特效提供一个全面的技术实现方案。核心任务是将一个外部定义的、动态的粒子效果系统，集成到Forge Modding API中。分析表明，所谓的“暴雪引擎”JSON格式在结构和功能上与Minecraft基岩版（Bedrock Edition）的粒子系统高度一致，这为我们提供了一个清晰、文档完备的参考模型。

### **1.1 建立工作模型：将基岩版粒子JSON作为分析蓝本**

**目标：** 通过论证“暴雪引擎”JSON格式与Minecraft基岩版粒子系统的相似性，建立一个具体且文档丰富的规范。这将为解析器的设计提供坚实的基础。

客户描述的系统包含一个主JSON文件，用于定义动画、运动轨迹和纹理引用，并附带一个独立的PNG纹理文件。这种结构与Minecraft基岩版的粒子系统完全吻合。在基岩版中，粒子效果同样由位于资源包particles文件夹下的.json文件定义。

基岩版的粒子格式是一个明确的基于组件的系统，通过组合不同的组件来实现复杂的行为 1。这与客户描述的在JSON中定义“运动轨迹”等属性的需求相符。一个典型的基岩版粒子JSON文件结构包含

format\_version、一个particle\_effect对象、一个包含identifier和basic\_render\_parameters（材质与纹理）的description对象，以及一个核心的components对象。这个结构将作为我们解析和实现的主要模板 1。

这种功能上的等价性并非巧合。客户提到该系统源于一个网页版粒子制作工具，而微软官方为基岩版开发者提供的粒子制作工具“Snowstorm”正是一个功能匹配的网页应用，其导出格式就是这种基于组件的JSON。此外，客户提及在更早的Minecraft 1.12.2版本中存在一个名为“龙核”的插件实现了类似功能，这表明将此特定粒子格式桥接到Java版的尝试早已有之。客户所描述的粒子运动、纹理映射和生命周期等核心功能，均可由基岩版的标准组件如minecraft:particle\_motion\_dynamic和minecraft:particle\_appearance\_billboard等精确定义 1。

因此，最有效且逻辑上最严谨的实现路径，是将此任务明确为实现一个**基岩版粒子JSON到Forge粒子API的转换器**。这消除了对“暴雪引擎”这一模糊概念的猜测，使我们能够直接利用基岩版详尽的官方和社区文档 1 作为源格式的权威规范。

### **1.2 关键数据结构：识别发射器、生命周期、运动和外观组件**

**目标：** 对需要解析和转换的核心基岩版组件进行分类，为数据处理流程提供清晰的路线图。

基岩版的粒子系统呈现出一个清晰的两层结构：发射器（Emitter）和粒子（Particle）。我们的实现架构必须反映这种层级关系，以确保逻辑的正确性和性能的优化。

* **发射器组件 (Emitter Components):** 这部分定义了粒子如何、何时以及在何处生成。关键组件包括：  
  * **速率控制:** 如 minecraft:emitter\_rate\_steady，定义了粒子生成的频率和最大数量 1。  
  * **生命周期控制:** 如 minecraft:emitter\_lifetime\_looping，控制发射器自身的激活和休眠循环 1。  
  * **形状控制:** 如 minecraft:emitter\_shape\_sphere，定义了粒子生成的空间形状（如点、球体、盒子等）和初始方向 1。  
* **粒子组件 (Particle Components):** 这部分定义了单个粒子在生成后的独立行为，是核心物理和视觉模拟发生的地方。我们将重点关注：  
  * **生命周期:** minecraft:particle\_lifetime\_expression 决定了粒子何时消亡 1。  
  * **运动:** minecraft:particle\_motion\_dynamic 通过线性和旋转加速度、阻力等参数控制粒子的物理轨迹 1。  
  * **外观:** minecraft:particle\_appearance\_billboard 控制粒子的尺寸、朝向和纹理UV坐标 1。  
    minecraft:particle\_appearance\_tinting 则负责其颜色和透明度的变化 1。

这种发射器与粒子的分离至关重要。发射器组件通常在生成循环开始或单个粒子发射时被求值一次，而粒子组件则需要在每个粒子存活的每一帧中都进行求值。这意味着我们的Mod需要一个两阶段的处理模型：首先，实现发射器逻辑，例如一个tick()循环，它根据emitter\_rate和emitter\_shape等组件的定义，决定在当前帧是否以及在何处生成新的粒子。其次，一旦一个粒子被生成，它自己的tick()方法将接管控制权，在每一帧中独立地评估其自身的particle\_motion和particle\_appearance等组件，直到其生命周期结束。这种架构下，一个“发射器”对象将管理成百上千个各自拥有独立状态的“粒子”对象，确保了系统的模块化和可扩展性。

### **1.3 MoLang的复杂性：理解动态表达式的核心作用**

**目标：** 揭示项目中最核心的技术挑战——嵌入式的MoLang表达式语言，并阐明为何它需要比简单数据映射复杂得多的解决方案。

在基岩版粒子JSON文件中，绝大多数数值并非静态常量，而是以字符串形式存在的MoLang表达式 1。例如，一个粒子的尺寸可能被定义为

variable.particle\_age / variable.particle\_lifetime，这将使其在生命周期内逐渐缩小。

MoLang语言提供了一个丰富的函数库（如math.sin、math.lerp）和一系列与上下文相关的变量（如variable.particle\_age、variable.emitter\_random\_1）1。这意味着，对于每一个粒子实例，在它的每一帧生命周期中，我们都必须动态地解析和计算这些字符串表达式，以获得其当前的加速度、颜色、尺寸等属性值。

客户认为渲染部分“很简单”的判断是正确的，因为最终都是绘制一个带纹理的四边形。然而，隐藏的复杂性在于**每一帧如何计算出这个四边形应该在何处、多大、什么颜色**。标准的JSON库（如Gson）能够解析JSON的结构，但它会将"math.sin(variable.particle\_age \* 90)"这样的表达式仅仅当作一个普通的字符串，无法计算其数学结果。

因此，本项目的核心技术难点并非JSON解析，而是**构建一个MoLang到Java的表达式求值引擎**。整个工程任务的重心从一个数据转换任务，转变为实现一个轻量级的脚本引擎。我们需要一个Java库或自研方案，它必须具备以下能力：

1. 从字符串中解析数学表达式。  
2. 支持自定义变量的注入（用于实现variable.particle\_age等）。  
3. 支持自定义函数的注册（用于实现MoLang的math.\*函数和query.\*查询）。

## **第二部分：构建基础：解析与表达式求值**

### **2.1 数据注入：选择JSON库并设计Java数据模型（POJO）**

**目标：** 规划初始的数据处理流程，包括读取JSON文件并将其反序列化为结构化、类型安全的Java对象。

为了处理JSON文件，我们需要一个稳定高效的Java库。Jackson和Gson都是业界公认的优秀选择。考虑到在Mod开发中通常倾向于轻量级和最小化依赖，Gson因其简洁的API（如new Gson().fromJson(...)）和易于集成的特性而成为一个理想的选择。

实现策略是设计一系列与基岩版JSON结构一一对应的POJO（Plain Old Java Object）类。例如，可以创建一个顶层的BlizzardParticleEffect.java类，它包含Description和Components两个子对象。而Components对象内部又会包含EmitterRateSteady、ParticleMotionDynamic等具体的组件类，每个类的字段都精确对应JSON中的键 1。这种方法提供了编译时类型检查，使得后续处理解析后的数据既安全又便捷，避免了繁琐且易错的手动遍历

JsonObject。

考虑到性能，对JSON文件的解析是一项消耗I/O和CPU资源的操作。如果在游戏中频繁触发粒子效果时每次都从磁盘重新读取和解析，将导致严重的性能卡顿。因此，必须实施一套懒加载与缓存机制。  
具体而言，需要创建一个ParticleEffectManager类。该管理器在游戏启动或首次使用时，扫描指定目录（例如assets/\<modid\>/blizzard\_particles/）下的所有粒子JSON文件。它会一次性将每个文件解析成对应的BlizzardParticleEffect对象，并将结果存储在一个Map\<ResourceLocation, BlizzardParticleEffect\>中。当游戏逻辑需要生成某个粒子效果时，它只需通过效果ID（ResourceLocation）向管理器请求预先解析好的对象，这是一个近乎瞬时的内存查找操作，从而避免了运行时的性能瓶颈。

### **2.2 实现MoLang求值器：动态值解析与计算策略**

**目标：** 详细设计并实现MoLang表达式求值引擎，这是整个项目的技术核心。

MoLang表达式的动态求值是实现粒子行为的关键。我们需要一个强大的Java表达式解析库。经过调研，Jep、JEL 和 **exp4j** 都是备选方案。其中，**exp4j** 是一个尤为合适的选择，因为它体积小（约40KB）、无任何外部依赖，并且其API明确支持自定义变量和自定义函数。这与我们处理MoLang中variable.\*变量和math.\*函数的需求完美契合。

实现的核心是创建一个MoLangEvaluator包装类。解析和执行表达式是计算密集型操作，尤其是在每一帧为成百上千个粒子执行此操作时。exp4j通过其ExpressionBuilder将字符串表达式“编译”成一个可复用的Expression对象，这个过程利用了调度场算法（Shunting-yard algorithm）将中缀表达式转换为更易于计算的后缀表达式（RPN）或抽象语法树（AST）。这个编译过程是整个流程中最为耗时的部分。

为了达到极致的性能，必须避免在每一帧中重复编译表达式字符串。最佳实践是：

1. **一次编译，多次求值：** 在每个粒子对象被创建时（即在其构造函数中），遍历其对应的BlizzardParticleEffect数据模型，将所有MoLang字符串表达式（如加速度、尺寸、颜色等）通过ExpressionBuilder编译成Expression对象。  
2. **缓存编译结果：** 将这些编译好的Expression对象存储在粒子实例的一个Map\<String, Expression\>中。  
3. **运行时更新与求值：** 在粒子每一帧的tick()方法中，只需通过expression.setVariable(...)方法更新variable.particle\_age等动态变量的当前值，然后调用evaluate()方法。evaluate()的执行速度极快，因为它操作的是已经编译好的内部结构，从而避免了每一帧都重新进行字符串解析和语法分析的巨大开销。

通过这种“编译一次，运行多次”的策略，可以将性能开销从每一帧的字符串解析，降低为每一帧的变量设置和快速数学运算，这是确保Mod在高粒子密度下依然流畅运行的关键。

### **表 2.1: MoLang 到 Java/exp4j 映射表**

下表为关键MoLang构造到其在Java和exp4j中实现方式的映射，可作为开发过程中的速查参考。

| MoLang 构造 | Java/Forge 等价物 | exp4j 实现策略 |
| :---- | :---- | :---- |
| variable.particle\_age | this.age (粒子存活tick数) | expression.setVariable("variable\_particle\_age", this.age / 20.0f); |
| variable.particle\_lifetime | this.lifetime (粒子最大存活tick数) | expression.setVariable("variable\_particle\_lifetime", this.lifetime / 20.0f); |
| variable.emitter\_random\_1 | this.emitterRandom1 (float) | 在发射器创建粒子时生成一个随机数并传入粒子构造函数。 |
| variable.particle\_random\_1 | this.particleRandom1 (float) | 在粒子构造函数中生成一个随机数并存储。 |
| math.sin(value) | Math.sin(Math.toRadians(value)) | new Function("math\_sin", 1\) {... }，注意MoLang使用角度而非弧度。 |
| math.cos(value) | Math.cos(Math.toRadians(value)) | new Function("math\_cos", 1\) {... }，同样需要角度到弧度的转换。 |
| math.lerp(a, b, t) | a \+ t \* (b \- a) | new Function("math\_lerp", 3\) {... }，实现线性插值。 |
| math.random(low, high) | low \+ this.random.nextFloat() \* (high \- low) | new Function("math\_random", 2\) {... }，生成指定范围的随机浮点数。 |

## **第三部分：深入Forge 1.20.1粒子引擎**

### **3.1 Forge粒子生命周期：注册ParticleType**

**目标：** 阐述在Forge中注册自定义粒子的基础流程，涵盖服务器端和客户端。

在Forge中，粒子与方块、物品一样，是需要注册的游戏对象。这一过程通过DeferredRegister\<ParticleType\<?\>\>来完成。ParticleType是粒子的唯一标识符。

对于简单的粒子效果，Forge提供了SimpleParticleType，它适用于那些不需要从服务器同步额外自定义数据的粒子。我们的系统恰好符合这一情况：所有复杂的行为逻辑都定义在客户端本地的JSON文件中，服务器只需告诉客户端在某个位置触发哪个效果即可，无需传输每个粒子的具体动态数据。

注册过程通常在Mod的主类或一个专门的注册管理类中进行，并将其绑定到Mod的事件总线上 3。一个关键的架构决策是，我们

**无需为每一个Blizzard JSON文件都注册一个独立的ParticleType**。传统的Forge粒子开发模式（如Kaupenjoe的教程所示）通常是一个ParticleType对应一种特定的粒子视觉效果 3。然而，我们的系统是数据驱动的，具体的粒子行为由加载的JSON文件动态决定，而非硬编码在Java类中。

因此，我们可以注册一个**单一的、通用的ParticleType**，例如BLIZZARD\_PARTICLE。当需要生成粒子时，我们通过一个自定义的网络数据包（详见第五部分）将具体的粒子效果ID（即JSON文件的ResourceLocation）从服务器发送到客户端。客户端在接收到数据包后，使用这个通用的ParticleType来生成粒子，并通过ID来加载对应的行为配置。这种方法极大地简化了注册流程，并使整个系统具备了高度的灵活性和可扩展性。

### **3.2 客户端工厂：实现并注册ParticleProvider**

**目标：** 详细说明如何创建并注册负责实例化我们自定义粒子类的客户端工厂。

ParticleProvider（在旧版本中称为IParticleFactory）是一个纯客户端的工厂类，其职责是根据给定的参数创建Particle的实例。这个工厂必须在客户端初始化阶段，通过监听RegisterParticleProvidersEvent事件来注册。

由于我们的粒子需要使用PNG纹理，这些纹理会被打包到Minecraft的粒子纹理图集（particle texture sheet）中。因此，我们必须使用event.registerSpriteSet()方法来注册我们的ParticleProvider。这个方法会向工厂提供一个SpriteSet对象，它是访问图集中特定纹理的关键 3。

我们的ParticleProvider实现将是整个系统的关键“注入点”。标准的createParticle方法签名中并没有参数可以让我们直接传入解析好的BlizzardParticleEffect数据对象。为了解决这个问题，需要一个巧妙的机制来传递上下文信息：

1. 服务器发送一个自定义网络数据包，其中包含要生成的粒子效果ID（ResourceLocation）和坐标。  
2. 客户端的网络处理器接收此数据包。在调用原版的ClientLevel\#addParticle方法之前，它将接收到的ResourceLocation存放在一个临时的、线程安全的变量中（例如ThreadLocal）。  
3. 游戏引擎随即调用我们注册的ParticleProvider的createParticle方法。  
4. 在createParticle方法内部，我们从该临时变量中读取ResourceLocation，然后使用它从缓存管理器中获取预先解析好的BlizzardParticleEffect对象。  
5. 最后，将这个数据对象连同其他标准参数一起，传递给我们自定义粒子类的构造函数。

通过这个流程，我们成功地将动态加载的数据注入到了Forge标准的粒子创建流程中。

### **3.3 粒子剖析：扩展TextureSheetParticle以实现自定义逻辑**

**目标：** 详细分解自定义粒子类的内部结构，这里是所有模拟和渲染逻辑的最终实现地。

为了简化渲染，我们的自定义粒子类（例如BlizzardParticle.java）应该继承自TextureSheetParticle。这个基类提供了处理从主粒子纹理图集中渲染、缩放和动画化的标准实现。

* **构造函数:** 这是粒子类最核心的部分。它除了接收ClientLevel、坐标、SpriteSet等标准参数外，还必须接收我们自定义的BlizzardParticleEffect数据对象。在构造函数中，必须完成所有一次性的设置工作，包括：  
  * 存储传入的BlizzardParticleEffect对象。  
  * 为MoLang中的variable.particle\_random\_\*和variable.emitter\_random\_\*等变量生成并存储随机值。  
  * 调用MoLangEvaluator，将JSON中所有的MoLang表达式字符串“编译”成可复用的exp4j Expression对象并缓存起来。  
* **tick() 方法:** 此方法每一帧被调用一次，是粒子行为的驱动核心。其内部逻辑严格遵循“更新-求值-应用”的模式：  
  1. **更新上下文:** 更新MoLang求值器中的变量，如this.age。  
  2. **求值表达式:** 遍历缓存的Expression对象，计算出当前帧的运动、颜色、尺寸等所有动态属性的浮点数值。  
  3. **应用物理:** 将计算出的加速度等值应用到粒子的速度和位置字段（this.xd, this.yd, this.zd, this.x, this.y, this.z）。  
  4. **应用视觉:** 将计算出的颜色、透明度和尺寸值应用到相应的渲染字段（this.rCol, this.gCol, this.bCol, this.alpha, this.quadSize）。  
  5. **处理生命周期:** 评估minecraft:particle\_lifetime\_expression，如果条件满足或this.age超过this.lifetime，则调用this.remove()销毁粒子。  
* **getRenderType() 方法:** 必须重写此方法并返回ParticleRenderType.PARTICLE\_SHEET\_TRANSLUCENT，以确保粒子能够正确地进行半透明渲染。

通过这种设计，每个BlizzardParticle实例都成为一个自包含的状态机。一旦由ParticleProvider创建，它就完全独立运行，其行为仅由其内部状态和时间流逝（this.age）决定。这种封装确保了即使在同屏存在大量粒子的情况下，系统也能高效、稳定地运行。

### **3.4 纹理与资源管理：链接粒子描述JSON**

**目标：** 阐明Forge引擎如何将一个ParticleType与其纹理资源关联起来，并解决动态纹理带来的挑战。

标准的Forge流程要求在assets/\<modid\>/particles/目录下为每个ParticleType创建一个同名的.json文件，即粒子描述文件。该文件内容很简单，只包含一个"textures"键，其值为一个ResourceLocation字符串数组，指向位于assets/\<modid\>/textures/particle/目录下的PNG纹理文件 3。

然而，这个静态映射机制与我们的动态系统存在根本冲突。在我们的系统中，粒子使用的纹理是在Blizzard JSON文件的description.texture字段中动态指定的 1，而不是在Mod代码中预先定义的。

为了解决这一矛盾，我们必须**绕过Forge标准的粒子描述JSON和SpriteSet系统**。具体实现如下：

1. 在注册ParticleType时，我们仍然需要提供一个空的或占位的粒子描述JSON文件以满足Forge的要求。  
2. 在我们的BlizzardParticle类中，我们将**忽略**由ParticleProvider传入的SpriteSet对象。  
3. 我们将实现自定义的渲染逻辑。不再依赖TextureSheetParticle的默认渲染，而是继承更底层的Particle类，并重写其render(VertexConsumer, Camera, float)方法。  
4. 在render方法中，我们从存储的BlizzardParticleEffect数据中获取纹理路径，通过Minecraft的TextureManager手动加载并绑定该纹理。然后，使用VertexConsumer手动绘制一个面向镜头的、应用了正确UV坐标的四边形。

这是一个重大的架构决策，它牺牲了TextureSheetParticle带来的一些便利性，但换来了支持完全数据驱动、动态纹理的强大能力，是成功实现该功能所必需的。

## **第四部分：转换层：将已解析数据映射至动态粒子**

### **4.1 中心枢纽：ParticleProvider在实例化中的作用**

**目标：** 通过代码级的示例，展示ParticleProvider如何将解析后的数据与新的粒子实例连接起来。

ParticleProvider作为数据注入的关键点，其实现将清晰地展示整个系统的连接逻辑。以下是其伪代码实现：

Java

public class BlizzardParticleProvider implements ParticleProvider\<SimpleParticleType\> {  
    private final SpriteSet spriteSet; // 尽管我们自定义渲染，但仍需接收它

    public BlizzardParticleProvider(SpriteSet spriteSet) {  
        this.spriteSet \= spriteSet;  
    }

    @Override  
    public Particle createParticle(SimpleParticleType type, ClientLevel level, double x, double y, double z, double xd, double yd, double zd) {  
        // 1\. 从线程局部变量中获取效果ID  
        ResourceLocation effectId \= ClientPacketHandler.getCurrentEffectId();  
        if (effectId \== null) {  
            return null; // 如果没有ID，则不创建粒子  
        }

        // 2\. 从管理器中获取预解析的数据  
        BlizzardParticleEffect effectData \= BlizzardParticleManager.getInstance().getEffect(effectId);  
        if (effectData \== null) {  
            return null; // 如果数据不存在，则不创建粒子  
        }

        // 3\. 实例化自定义粒子，并注入数据  
        return new BlizzardParticle(level, x, y, z, xd, yd, zd, effectData, this.spriteSet);  
    }  
}

这段代码清晰地演示了ParticleProvider如何充当桥梁，将通过网络传递的动态上下文（effectId）与静态缓存的数据（effectData）结合，最终实例化出一个完全配置好的自定义粒子。

### **4.2 赋予粒子生命：tick()方法的实现**

**目标：** 详细阐述自定义粒子tick()方法中的核心模拟循环。

BlizzardParticle的tick()方法是粒子效果得以呈现的核心。它将JSON中定义的静态规则，转化为每一帧的动态变化。

**tick() 方法实现步骤:**

1. **调用父类方法并更新年龄：**  
   Java  
   super.tick(); // 处理基础物理和年龄增长 (this.age++)  
   if (this.age \> this.lifetime) {  
       this.remove(); // 如果超过最大生命周期则移除  
       return;  
   }

2. **更新MoLang上下文：**  
   Java  
   float particleAge \= this.age / 20.0f;  
   float lifeRatio \= particleAge / (this.lifetime / 20.0f);  
   moLangEvaluator.setVariable("variable.particle\_age", particleAge);  
   moLangEvaluator.setVariable("variable.particle\_lifetime", this.lifetime / 20.0f);  
   //... 更新其他可能变化的变量

3. **求值并应用动态属性（物理）：**  
   Java  
   // 从 minecraft:particle\_motion\_dynamic 获取  
   Vec3 acceleration \= moLangEvaluator.evaluateVec3("linear\_acceleration");  
   float drag \= moLangEvaluator.evaluateFloat("linear\_drag\_coefficient");

   // 应用加速度  
   this.xd \+= acceleration.x / 20.0f; // tick频率为20Hz  
   this.yd \+= acceleration.y / 20.0f;  
   this.zd \+= acceleration.z / 20.0f;

   // 应用阻力  
   this.xd \*= (1.0f \- drag / 20.0f);  
   this.yd \*= (1.0f \- drag / 20.0f);  
   this.zd \*= (1.0f \- drag / 20.0f);

4. **求值并应用动态属性（视觉）：**  
   Java  
   // 从 minecraft:particle\_appearance\_billboard 获取  
   Vec2 size \= moLangEvaluator.evaluateVec2("size");  
   this.quadSize \= (size.x \+ size.y) / 2.0f; // 简化处理，或分别处理宽高

   // 从 minecraft:particle\_appearance\_tinting 获取  
   float interpolant \= moLangEvaluator.evaluateFloat("color\_interpolant");  
   Color color \= effectData.getColorAt(interpolant); // 假设effectData中有处理颜色渐变的方法  
   this.setColor(color.getRed() / 255f, color.getGreen() / 255f, color.getBlue() / 255f);  
   this.setAlpha(color.getAlpha() / 255f);

5. **处理生命周期表达式：**  
   Java  
   // 从 minecraft:particle\_lifetime\_expression 获取  
   if (moLangEvaluator.evaluateBoolean("expiration\_expression")) {  
       this.remove();  
   }

### **4.3 发射器逻辑：管理生成速率、形状和生命周期**

**目标：** 设计用于读取emitter组件并随时间管理粒子生成的系统。

发射器的逻辑不应存在于单个粒子中，因为它是一个持续生成多个粒子的过程。为此，我们需要创建一个新的BlizzardParticleEmitter类。当一个粒子效果被触发时，客户端会在指定位置创建一个该类的实例。

这个发射器对象是一个短暂的、纯客户端的实体，其tick()方法负责：

* **管理自身状态：** 根据minecraft:emitter\_lifetime\_looping等组件，更新自身的年龄、循环次数等状态。  
* **决定生成数量：** 根据minecraft:emitter\_rate\_steady等组件，计算当前帧需要生成多少个新粒子。  
* **计算初始状态：** 对于每个要生成的新粒子，根据minecraft:emitter\_shape\_sphere等形状组件，计算出其相对于发射器位置的初始坐标偏移和初始速度矢量 1。  
* **生成粒子：** 调用ClientLevel.addParticle()，传入我们通用的ParticleType和计算出的初始状态，从而创建新的BlizzardParticle实例。

BlizzardParticleManager将维护一个当前所有活动发射器的列表，并在客户端的每一帧中调用它们的tick()方法。当一个发射器的生命周期组件判定其已结束时，管理器会将其从活动列表中移除。这种架构精确地模拟了源格式中发射器和粒子的职责分离，为管理复杂、持续的粒子效果提供了清晰的实现路径。

### **表 4.1: Blizzard/基岩版 JSON 到 Forge Particle 属性映射表**

下表详细列出了关键JSON属性到Forge Particle类字段的映射关系，为tick()方法的具体实现提供了直接指导。

| Blizzard/基岩版组件与属性 | 目标Forge Particle字段 | 实现说明 |
| :---- | :---- | :---- |
| minecraft:particle\_motion\_dynamic.linear\_acceleration | this.xd, this.yd, this.zd | 每帧通过MoLang求值，将结果作为加速度累加到速度上。 |
| minecraft:particle\_motion\_dynamic.linear\_drag\_coefficient | this.xd, this.yd, this.zd | 每帧通过MoLang求值，将速度乘以(1 \- drag)。 |
| minecraft:particle\_appearance\_billboard.size | this.quadSize | 每帧通过MoLang求值，直接设置粒子渲染尺寸。 |
| minecraft:particle\_appearance\_tinting.color (Gradient) | this.rCol, this.gCol, this.bCol | 每帧求值interpolant表达式，然后在颜色渐变数组中进行线性插值（LERP）得到当前颜色。 |
| minecraft:particle\_lifetime\_expression.max\_lifetime | this.lifetime | 在粒子构造时通过MoLang求值一次，设定最大存活tick数。 |
| minecraft:particle\_lifetime\_expression.expiration\_expression | this.remove() | 每帧求值该MoLang表达式，若结果为非零，则调用移除方法。 |

## **第五部分：集成与最终实现**

### **5.1 粒子系统管理器：加载、缓存与生成效果的稳健实现**

**目标：** 正式化设计一个高级管理器类，用于协调整个粒子系统。

BlizzardParticleManager是整个系统的中心枢纽，它将作为一个单例存在，负责以下核心功能：

* **资源加载与解析：** 在游戏加载阶段（例如FMLClientSetupEvent），使用Minecraft的ResourceManager来发现并读取所有位于assets/\<modid\>/blizzard\_particles/下的.json和关联的.png文件。使用Gson将JSON文件解析为BlizzardParticleEffect对象。  
* **数据缓存：** 将解析后的BlizzardParticleEffect对象存储在一个Map中，以其ResourceLocation为键，供随时快速访问。  
* **公共API：** 提供一个简单的静态方法，如spawnEffect(Level level, ResourceLocation effectId, Vec3 position)。Mod的其他部分（例如物品的使用效果、方块的破坏效果等）可以通过调用此方法来触发粒子效果。  
* **发射器管理：** 在其内部维护一个活动BlizzardParticleEmitter实例的列表。通过监听客户端的ClientTickEvent，在每一帧中遍历并更新所有活动发射器，同时移除已经结束生命周期的发射器。

### **5.2 网络同步：从服务器触发客户端渲染**

**目标：** 阐述在多人游戏环境中正确触发粒子效果的必要性和实现方法。

粒子效果是纯客户端的视觉表现，其相关类在专有服务器上甚至不存在。因此，任何直接在通用代码（common code）中生成粒子的尝试都会在服务器端导致崩溃。为了确保Mod在单人游戏和多人服务器上都能正确工作，必须通过网络通信来同步效果。

**实现流程：**

1. **定义数据包：** 使用Forge的SimpleChannel网络API定义一个自定义数据包，例如SpawnBlizzardParticlePacket。该数据包需要包含两个关键信息：要生成的粒子效果ID (ResourceLocation) 和生成位置 (Vec3)。  
2. **服务器端发送：** 当服务器端发生需要触发粒子效果的事件时（例如，玩家使用某个物品），服务器逻辑将构建一个SpawnBlizzardParticlePacket实例，并将其发送给所有需要看到该效果的客户端（例如，在指定位置附近的所有玩家）。  
3. **客户端接收与处理：** 在客户端注册该数据包的处理器。当客户端接收到SpawnBlizzardParticlePacket时，处理器会提取出效果ID和位置信息，然后调用BlizzardParticleManager.getInstance().spawnEffect(...)方法。  
4. **客户端执行：** BlizzardParticleManager接收到调用后，在本地创建并管理BlizzardParticleEmitter，开始在客户端渲染粒子效果。

这个流程确保了游戏逻辑的权威源头在服务器，而视觉表现则完全由客户端负责。这是开发功能完善、多人兼容的Mod的标准实践。

### **5.3 结论与高级建议**

**目标：** 总结实现路径，并为未来的开发和优化提供建议。

本报告详细规划了一条将动态的、基于基岩版JSON格式的粒子系统移植到Minecraft 1.20.1 Forge环境的技术路线。核心在于识别出MoLang表达式求值是主要的技术挑战，并为此设计了一个基于exp4j库的、高性能的求值引擎。同时，报告解决了Forge粒子API与动态数据源之间的多个架构性冲突，提出了包括单一通用ParticleType、通过网络包注入上下文到ParticleProvider、以及自定义渲染以支持动态纹理等一系列解决方案。

**实施建议：**

* **分阶段实施：** 建议采用增量式开发策略。首先实现一个能显示单个静态纹理、不会移动的粒子。在此基础上，逐步增加动态运动、颜色/尺寸变化，最后再实现复杂的发射器逻辑。这种方法有助于隔离问题，简化调试过程。  
* **性能调优：** 密切关注性能，尤其是在处理大量粒子或复杂MoLang表达式时。可以在Mod的配置文件中加入粒子数量上限的选项，以允许玩家根据自己的硬件配置进行调整。  
* **调试策略：** 在开发过程中，可以利用游戏内聊天框、日志或调试HUD，实时输出关键MoLang表达式的求值结果。这对于验证求值器是否按预期工作非常有帮助。  
* **未来扩展：** 本方案为处理更高级的基岩版组件（如minecraft:particle\_motion\_collision的碰撞检测，或minecraft:particle\_lifetime\_events的事件时间轴）打下了基础。这些功能的实现将需要引入客户端的物理碰撞检测逻辑和一个简单的事件系统。

#### **引用的著作**

1. Particles Documentation | bedrock.dev, 访问时间为 八月 6, 2025， [https://bedrock.dev/docs/stable/Particles](https://bedrock.dev/docs/stable/Particles)  
2. Molang Documentation | bedrock.dev, 访问时间为 八月 6, 2025， [https://bedrock.dev/docs/stable/Molang](https://bedrock.dev/docs/stable/Molang)  
3. 访问时间为 一月 1, 1970， [https://vscode.dev/github/Tutorials-By-Kaupenjoe/Forge-Tutorial-1.20.X](https://vscode.dev/github/Tutorials-By-Kaupenjoe/Forge-Tutorial-1.20.X)